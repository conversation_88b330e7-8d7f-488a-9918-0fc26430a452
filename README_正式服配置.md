# 正式服多线程启动脚本使用说明

## 🎯 当前配置

### 正式服区服配置
- **1区** - 端口 5001
- **4区** - 端口 5004  
- **5区** - 端口 5005
- **6区** - 端口 5006 (预留)

## 🚀 使用方法

### 启动所有服务
```bash
./start.sh start
```

### 停止所有服务
```bash
./start.sh stop
```

### 重启所有服务
```bash
./start.sh restart
```

### 查看服务状态
```bash
./start.sh status
```

### 实时监控
```bash
./start.sh monitor
```

### 重启指定区服
```bash
./start.sh restart-zone 1    # 重启1区
./start.sh restart-zone 4    # 重启4区
./start.sh restart-zone 5    # 重启5区
```

## 📋 如何添加新区服 (例如6区)

### 方法1: 使用配置文件 (推荐)

1. **编辑区服配置文件**
```bash
vim zone_config.sh
```

2. **在 ZONE_CONFIGS 数组中添加新区服**
```bash
ZONE_CONFIGS=(
    "1:5001"    # 1区 - 端口5001
    "4:5004"    # 4区 - 端口5004  
    "5:5005"    # 5区 - 端口5005
    "6:5006"    # 6区 - 端口5006 ← 取消注释或添加这行
)
```

3. **保存文件，重启脚本即可**

### 方法2: 查看当前配置
```bash
./zone_config.sh show    # 显示当前区服配置
./zone_config.sh list    # 列出所有区服ID
```

## 🔧 脚本特性

### 自动化功能
- ✅ **自动检测** - 避免重复启动已运行的服务
- ✅ **智能等待** - 区服启动间隔3秒，避免数据库竞争
- ✅ **错误处理** - 启动失败自动清理PID文件
- ✅ **日志记录** - 所有操作记录到日志文件

### 监控功能
- ✅ **进程监控** - 实时显示服务状态
- ✅ **资源监控** - CPU、内存使用情况
- ✅ **端口检测** - 自动检测端口占用

### 配置管理
- ✅ **配置文件** - 统一管理区服配置
- ✅ **动态加载** - 修改配置无需改动主脚本
- ✅ **扩展性强** - 轻松添加新区服

## 📁 文件结构

```
├── start.sh              # 主启动脚本
├── zone_config.sh         # 区服配置文件
├── README_正式服配置.md   # 使用说明
├── /data/server/logs/     # 日志目录
├── /data/server/pids/     # PID文件目录
└── /data/server/monitor/  # 监控文件目录
```

## ⚠️ 注意事项

1. **启动顺序**: 脚本会按顺序启动服务，请勿手动干预
2. **端口冲突**: 确保配置的端口未被其他程序占用
3. **权限问题**: 确保脚本有执行权限 `chmod +x start.sh`
4. **数据库连接**: 区服启动有3秒间隔，避免数据库连接竞争

## 🆘 故障排除

### 服务启动失败
```bash
# 查看详细日志
tail -f /data/server/logs/Zone_1.log

# 检查端口占用
netstat -tlnp | grep 5001

# 手动停止进程
./start.sh stop
```

### 添加新区服后无法启动
1. 检查 `zone_config.sh` 语法是否正确
2. 确保端口号未被占用
3. 检查区服ID是否重复

## 📞 技术支持

如有问题，请检查：
1. 日志文件: `/data/server/logs/startup.log`
2. 服务状态: `./start.sh status`
3. 系统资源: `./start.sh monitor`
