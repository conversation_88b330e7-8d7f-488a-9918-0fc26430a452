#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
游戏服务器性能监控脚本
监控CPU、内存、网络、数据库连接等关键指标
"""

import os
import sys
import time
import json
import psutil
import requests
import threading
from datetime import datetime
from collections import defaultdict, deque

class GameServerMonitor:
    def __init__(self):
        self.monitor_data = defaultdict(deque)
        self.alert_thresholds = {
            'cpu_usage': 80,        # CPU使用率阈值
            'memory_usage': 85,     # 内存使用率阈值
            'disk_usage': 90,       # 磁盘使用率阈值
            'response_time': 1000,  # 响应时间阈值(ms)
            'error_rate': 5,        # 错误率阈值(%)
        }
        self.services = {
            'backend': ['8500', '8501', '8502', '8503'],
            'fight': ['3001', '3002'],
            'zone': ['5001', '5002', '5003', '5004'],
            'analytics': ['7701']
        }
        self.running = True
        
    def get_system_metrics(self):
        """获取系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # 磁盘使用率
            disk = psutil.disk_usage('/data')
            disk_percent = (disk.used / disk.total) * 100
            
            # 网络IO
            net_io = psutil.net_io_counters()
            
            # 负载平均值
            load_avg = os.getloadavg()
            
            return {
                'timestamp': datetime.now().isoformat(),
                'cpu_percent': cpu_percent,
                'memory_percent': memory_percent,
                'memory_used_gb': memory.used / (1024**3),
                'memory_total_gb': memory.total / (1024**3),
                'disk_percent': disk_percent,
                'disk_used_gb': disk.used / (1024**3),
                'disk_total_gb': disk.total / (1024**3),
                'network_bytes_sent': net_io.bytes_sent,
                'network_bytes_recv': net_io.bytes_recv,
                'load_avg_1m': load_avg[0],
                'load_avg_5m': load_avg[1],
                'load_avg_15m': load_avg[2],
            }
        except Exception as e:
            print(f"获取系统指标失败: {e}")
            return None
    
    def get_process_metrics(self):
        """获取进程指标"""
        process_metrics = []
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'memory_percent', 'memory_info']):
                try:
                    pinfo = proc.info
                    cmdline = ' '.join(pinfo['cmdline']) if pinfo['cmdline'] else ''
                    
                    # 检查是否是游戏服务进程
                    if any(keyword in cmdline.lower() for keyword in ['python', 'node', 'server.py', 'laya.js', 'manage.py']):
                        if any(port in cmdline for service_ports in self.services.values() for port in service_ports):
                            process_metrics.append({
                                'pid': pinfo['pid'],
                                'name': pinfo['name'],
                                'cmdline': cmdline,
                                'cpu_percent': pinfo['cpu_percent'],
                                'memory_percent': pinfo['memory_percent'],
                                'memory_mb': pinfo['memory_info'].rss / (1024**2) if pinfo['memory_info'] else 0,
                            })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                    
        except Exception as e:
            print(f"获取进程指标失败: {e}")
            
        return process_metrics
    
    def check_service_health(self, service_type, port):
        """检查服务健康状态"""
        try:
            if service_type == 'fight':
                # Node.js战斗服务健康检查
                url = f"http://127.0.0.1:{port}/getVersion/"
                response = requests.post(url, json={}, timeout=5)
            else:
                # Python服务健康检查
                url = f"http://127.0.0.1:{port}/health/"
                response = requests.get(url, timeout=5)
            
            return {
                'status': 'healthy' if response.status_code == 200 else 'unhealthy',
                'response_time': response.elapsed.total_seconds() * 1000,
                'status_code': response.status_code
            }
        except requests.exceptions.RequestException as e:
            return {
                'status': 'unreachable',
                'response_time': -1,
                'error': str(e)
            }
    
    def get_service_metrics(self):
        """获取服务指标"""
        service_metrics = {}
        
        for service_type, ports in self.services.items():
            service_metrics[service_type] = {}
            for port in ports:
                health = self.check_service_health(service_type, port)
                service_metrics[service_type][port] = health
                
        return service_metrics
    
    def check_database_connections(self):
        """检查数据库连接"""
        try:
            # 这里可以添加具体的数据库连接检查逻辑
            # 例如检查MySQL连接池状态、Redis连接状态等
            return {
                'mysql_connections': 'unknown',  # 需要实现具体检查逻辑
                'redis_connections': 'unknown',   # 需要实现具体检查逻辑
            }
        except Exception as e:
            return {'error': str(e)}
    
    def analyze_performance(self, metrics):
        """分析性能指标并生成告警"""
        alerts = []
        
        # 检查CPU使用率
        if metrics['system']['cpu_percent'] > self.alert_thresholds['cpu_usage']:
            alerts.append({
                'type': 'cpu_high',
                'message': f"CPU使用率过高: {metrics['system']['cpu_percent']:.1f}%",
                'severity': 'warning'
            })
        
        # 检查内存使用率
        if metrics['system']['memory_percent'] > self.alert_thresholds['memory_usage']:
            alerts.append({
                'type': 'memory_high',
                'message': f"内存使用率过高: {metrics['system']['memory_percent']:.1f}%",
                'severity': 'warning'
            })
        
        # 检查磁盘使用率
        if metrics['system']['disk_percent'] > self.alert_thresholds['disk_usage']:
            alerts.append({
                'type': 'disk_high',
                'message': f"磁盘使用率过高: {metrics['system']['disk_percent']:.1f}%",
                'severity': 'critical'
            })
        
        # 检查服务响应时间
        for service_type, service_data in metrics['services'].items():
            for port, health in service_data.items():
                if health['response_time'] > self.alert_thresholds['response_time']:
                    alerts.append({
                        'type': 'slow_response',
                        'message': f"服务 {service_type}:{port} 响应时间过长: {health['response_time']:.0f}ms",
                        'severity': 'warning'
                    })
                
                if health['status'] != 'healthy':
                    alerts.append({
                        'type': 'service_down',
                        'message': f"服务 {service_type}:{port} 状态异常: {health['status']}",
                        'severity': 'critical'
                    })
        
        return alerts
    
    def save_metrics(self, metrics):
        """保存监控数据"""
        try:
            # 保存到文件
            log_file = f"/data/server/monitor/metrics_{datetime.now().strftime('%Y%m%d')}.json"
            os.makedirs(os.path.dirname(log_file), exist_ok=True)
            
            with open(log_file, 'a') as f:
                f.write(json.dumps(metrics, ensure_ascii=False) + '\n')
                
            # 保存最近的数据到内存（用于实时监控）
            self.monitor_data['recent'].append(metrics)
            if len(self.monitor_data['recent']) > 100:  # 只保留最近100条记录
                self.monitor_data['recent'].popleft()
                
        except Exception as e:
            print(f"保存监控数据失败: {e}")
    
    def print_dashboard(self, metrics):
        """打印监控面板"""
        os.system('clear')  # 清屏
        
        print("=" * 80)
        print(f"游戏服务器监控面板 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        # 系统指标
        sys_metrics = metrics['system']
        print(f"\n📊 系统指标:")
        print(f"  CPU使用率:    {sys_metrics['cpu_percent']:6.1f}%")
        print(f"  内存使用率:   {sys_metrics['memory_percent']:6.1f}% ({sys_metrics['memory_used_gb']:.1f}GB/{sys_metrics['memory_total_gb']:.1f}GB)")
        print(f"  磁盘使用率:   {sys_metrics['disk_percent']:6.1f}% ({sys_metrics['disk_used_gb']:.1f}GB/{sys_metrics['disk_total_gb']:.1f}GB)")
        print(f"  负载平均值:   {sys_metrics['load_avg_1m']:.2f}, {sys_metrics['load_avg_5m']:.2f}, {sys_metrics['load_avg_15m']:.2f}")
        
        # 服务状态
        print(f"\n🚀 服务状态:")
        for service_type, service_data in metrics['services'].items():
            print(f"  {service_type.upper()}:")
            for port, health in service_data.items():
                status_icon = "✅" if health['status'] == 'healthy' else "❌"
                response_time = f"{health['response_time']:.0f}ms" if health['response_time'] > 0 else "N/A"
                print(f"    端口 {port}: {status_icon} {health['status']} ({response_time})")
        
        # 进程信息
        if metrics['processes']:
            print(f"\n🔧 进程信息:")
            print(f"  {'PID':<8} {'名称':<15} {'CPU%':<8} {'内存%':<8} {'内存(MB)':<10}")
            print(f"  {'-'*8} {'-'*15} {'-'*8} {'-'*8} {'-'*10}")
            for proc in metrics['processes'][:10]:  # 只显示前10个进程
                print(f"  {proc['pid']:<8} {proc['name']:<15} {proc['cpu_percent']:<8.1f} {proc['memory_percent']:<8.1f} {proc['memory_mb']:<10.1f}")
        
        # 告警信息
        if metrics['alerts']:
            print(f"\n⚠️  告警信息:")
            for alert in metrics['alerts']:
                severity_icon = "🔴" if alert['severity'] == 'critical' else "🟡"
                print(f"  {severity_icon} {alert['message']}")
        else:
            print(f"\n✅ 无告警信息")
        
        print("\n" + "=" * 80)
        print("按 Ctrl+C 退出监控")
    
    def run_monitor(self, interval=5, dashboard=False):
        """运行监控"""
        print(f"开始监控游戏服务器性能 (间隔: {interval}秒)")
        
        try:
            while self.running:
                # 收集指标
                system_metrics = self.get_system_metrics()
                process_metrics = self.get_process_metrics()
                service_metrics = self.get_service_metrics()
                database_metrics = self.check_database_connections()
                
                if system_metrics:
                    metrics = {
                        'timestamp': system_metrics['timestamp'],
                        'system': system_metrics,
                        'processes': process_metrics,
                        'services': service_metrics,
                        'database': database_metrics,
                        'alerts': []
                    }
                    
                    # 分析性能并生成告警
                    alerts = self.analyze_performance(metrics)
                    metrics['alerts'] = alerts
                    
                    # 保存数据
                    self.save_metrics(metrics)
                    
                    # 显示面板
                    if dashboard:
                        self.print_dashboard(metrics)
                    else:
                        # 只打印告警
                        if alerts:
                            print(f"[{datetime.now().strftime('%H:%M:%S')}] 发现 {len(alerts)} 个告警:")
                            for alert in alerts:
                                print(f"  - {alert['message']}")
                        else:
                            print(f"[{datetime.now().strftime('%H:%M:%S')}] 系统运行正常")
                
                time.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n监控已停止")
            self.running = False

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='游戏服务器性能监控')
    parser.add_argument('--interval', type=int, default=5, help='监控间隔(秒)')
    parser.add_argument('--dashboard', action='store_true', help='显示实时面板')
    
    args = parser.parse_args()
    
    monitor = GameServerMonitor()
    monitor.run_monitor(interval=args.interval, dashboard=args.dashboard)

if __name__ == '__main__':
    main()
