# 多区服扩展指南

## 🎯 当前优化策略

基于您8核16G阿里云服务器的配置，我们采用**多线程优化**而不是多进程，原因：

### 为什么选择多线程？

1. **共享游戏状态**: 您的项目有大量共享数据（User.users、world.cities等）
2. **避免数据同步**: 多进程需要复杂的进程间通信
3. **内存效率**: 单进程内存占用更少
4. **扩展性更好**: 线程数量可以根据区服数量动态调整

## 📊 当前配置

```bash
# 8核16G服务器配置
后台服务: 1个进程 + 30个API线程
区服服务: 1个进程 + 20个处理线程  
战斗服务: 2个进程（无状态，可以多进程）
```

## 🚀 区服扩展方案

### 2-5个区服（当前阶段）
```
线程配置: API线程30个 + 处理线程20个
预期负载: 轻松支持
资源使用: CPU 40-60%, 内存 6-8GB
```

### 6-10个区服（中期扩展）
```bash
# 调整线程池配置
API线程: 30 → 50个
处理线程: 20 → 30个
数据库连接: 15 → 25个
Redis连接: 50 → 80个
```

### 11-20个区服（大规模扩展）
```bash
# 需要升级服务器配置
推荐配置: 16核32G
API线程: 50 → 80个
处理线程: 30 → 50个
数据库连接: 25 → 40个
Redis连接: 80 → 120个
```

## 🔧 扩展时需要调整的参数

### 1. 线程池配置
```python
# server/service/server.py
class Api:
    executor = ThreadPoolExecutor(区服数量 * 3)  # 每区服3个API线程

class RequestCollection:
    executor = ThreadPoolExecutor(区服数量 * 2)  # 每区服2个处理线程
```

### 2. 数据库连接池
```python
# server/trunk/game_lib/game_lib/db/database.py
pool_size = 15 + (区服数量 * 2)      # 基础15个 + 每区服2个
max_overflow = 25 + (区服数量 * 3)   # 基础25个 + 每区服3个
```

### 3. Redis连接池
```python
# server/trunk/game_lib/game_lib/libs/rediscache.py
max_connections = 50 + (区服数量 * 5)  # 基础50个 + 每区服5个
```

## 📈 性能监控指标

### 关键监控点
1. **线程使用率**: 不应超过80%
2. **数据库连接数**: 监控连接池使用情况
3. **内存使用**: 每个区服约增加800MB-1.2GB
4. **CPU使用率**: 不应持续超过70%

### 告警阈值
```
线程池使用率 > 80%     → 需要增加线程数
数据库连接数 > 90%     → 需要增加连接池
内存使用率 > 85%       → 需要升级服务器
CPU使用率 > 75%       → 需要优化或升级
```

## 🛠️ 扩展操作步骤

### 添加新区服时：

1. **评估当前负载**
```bash
./start_all_optimized.sh status
python monitor_performance.py --dashboard
```

2. **调整配置参数**
```bash
# 根据区服数量调整线程池大小
# 修改 server/service/server.py 中的线程数
```

3. **测试性能**
```bash
# 在低峰期部署测试
./start_all_optimized.sh restart
# 监控30分钟确保稳定
```

4. **监控关键指标**
- 响应时间是否增加
- 错误率是否上升
- 资源使用是否正常

## 💡 优化建议

### 短期优化（当前8核16G）
- ✅ 已优化数据库连接池
- ✅ 已优化Redis连接池  
- ✅ 已优化线程池配置
- 🔄 可支持5-8个区服

### 中期优化（需要时）
- 🔄 升级到16核32G
- 🔄 使用Redis集群
- 🔄 数据库读写分离
- 🔄 可支持15-20个区服

### 长期优化（大规模）
- 🔄 微服务拆分
- 🔄 负载均衡集群
- 🔄 分布式缓存
- 🔄 可支持50+区服

## ⚠️ 注意事项

1. **内存监控**: 每增加一个区服，内存使用约增加1GB
2. **数据库压力**: 区服增加时，数据库是主要瓶颈
3. **备份策略**: 多区服需要考虑数据备份策略
4. **故障隔离**: 虽然共享状态，但要防止单区服问题影响全服

## 📞 扩展检查清单

添加新区服前检查：
- [ ] 当前CPU使用率 < 60%
- [ ] 当前内存使用率 < 70%  
- [ ] 数据库连接池使用率 < 70%
- [ ] Redis连接池使用率 < 70%
- [ ] 近期无性能问题
- [ ] 已备份当前配置

满足以上条件才建议添加新区服。
