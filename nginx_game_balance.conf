# Nginx负载均衡配置 - 游戏服务器
# 将此配置添加到nginx.conf或单独的配置文件中

# 后台服务负载均衡
upstream backend_servers {
    # 使用least_conn算法，将请求分发到连接数最少的服务器
    least_conn;
    
    # 后台服务实例
    server 127.0.0.1:8500 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:8501 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:8502 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:8503 weight=1 max_fails=3 fail_timeout=30s;
    
    # 保持连接
    keepalive 32;
}

# 战斗服务负载均衡
upstream fight_servers {
    # 使用ip_hash确保同一用户的请求分发到同一服务器
    ip_hash;
    
    # 战斗服务实例
    server 127.0.0.1:3001 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:3002 weight=1 max_fails=3 fail_timeout=30s;
    
    # 保持连接
    keepalive 16;
}

# 区服负载均衡
upstream zone_servers {
    # 使用least_conn算法
    least_conn;
    
    # 区服实例
    server 127.0.0.1:5001 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:5002 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:5003 weight=1 max_fails=3 fail_timeout=30s;
    server 127.0.0.1:5004 weight=1 max_fails=3 fail_timeout=30s;
    
    # 保持连接
    keepalive 32;
}

# 主服务器配置
server {
    listen 80;
    server_name your-game-server.com;  # 替换为您的域名
    
    # 日志配置
    access_log /var/log/nginx/game_access.log;
    error_log /var/log/nginx/game_error.log;
    
    # 通用配置
    client_max_body_size 10M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    send_timeout 60s;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain application/json application/javascript text/css;
    
    # 后台管理接口
    location /admin/ {
        proxy_pass http://backend_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 连接配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        
        # 保持连接
        proxy_http_version 1.1;
        proxy_set_header Connection "";
    }
    
    # 游戏API接口
    location /api/ {
        proxy_pass http://backend_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 连接配置
        proxy_connect_timeout 10s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        proxy_buffering off;  # 游戏API需要实时响应
        
        # 保持连接
        proxy_http_version 1.1;
        proxy_set_header Connection "";
    }
    
    # 战斗服务接口
    location /fight/ {
        proxy_pass http://fight_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 战斗服务需要快速响应
        proxy_connect_timeout 5s;
        proxy_send_timeout 15s;
        proxy_read_timeout 15s;
        proxy_buffering off;
        
        # 保持连接
        proxy_http_version 1.1;
        proxy_set_header Connection "";
    }
    
    # 区服接口
    location /zone/ {
        proxy_pass http://zone_servers;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 连接配置
        proxy_connect_timeout 10s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        proxy_buffering off;
        
        # 保持连接
        proxy_http_version 1.1;
        proxy_set_header Connection "";
    }
    
    # WebSocket支持（如果需要）
    location /ws/ {
        proxy_pass http://zone_servers;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket配置
        proxy_connect_timeout 7d;
        proxy_send_timeout 7d;
        proxy_read_timeout 7d;
    }
    
    # 静态资源
    location /static/ {
        alias /data/server/static/;
        expires 1d;
        add_header Cache-Control "public, immutable";
    }
    
    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # 服务器状态
    location /nginx_status {
        stub_status on;
        access_log off;
        allow 127.0.0.1;
        deny all;
    }
}

# HTTPS配置（推荐生产环境使用）
server {
    listen 443 ssl http2;
    server_name your-game-server.com;  # 替换为您的域名
    
    # SSL证书配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # SSL优化配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 其他配置与HTTP相同
    # ... (复制上面的location配置)
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name your-game-server.com;
    return 301 https://$server_name$request_uri;
}
