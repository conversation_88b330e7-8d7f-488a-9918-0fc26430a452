-- MySQL性能优化配置
-- 请根据服务器配置调整这些参数

-- 连接相关配置
SET GLOBAL max_connections = 500;                    -- 最大连接数
SET GLOBAL max_user_connections = 450;               -- 每用户最大连接数
SET GLOBAL connect_timeout = 10;                     -- 连接超时
SET GLOBAL wait_timeout = 3600;                      -- 等待超时(1小时)
SET GLOBAL interactive_timeout = 3600;               -- 交互超时(1小时)

-- 缓冲区配置
SET GLOBAL innodb_buffer_pool_size = '1G';           -- InnoDB缓冲池大小(根据内存调整)
SET GLOBAL innodb_log_buffer_size = 16777216;        -- 日志缓冲区大小(16MB)
SET GLOBAL key_buffer_size = 268435456;              -- MyISAM键缓冲区(256MB)
SET GLOBAL read_buffer_size = 2097152;               -- 读缓冲区(2MB)
SET GLOBAL read_rnd_buffer_size = 8388608;           -- 随机读缓冲区(8MB)
SET GLOBAL sort_buffer_size = 4194304;               -- 排序缓冲区(4MB)
SET GLOBAL join_buffer_size = 4194304;               -- 连接缓冲区(4MB)

-- 查询缓存配置
SET GLOBAL query_cache_type = 1;                     -- 启用查询缓存
SET GLOBAL query_cache_size = 134217728;             -- 查询缓存大小(128MB)
SET GLOBAL query_cache_limit = 2097152;              -- 单个查询缓存限制(2MB)

-- InnoDB配置
SET GLOBAL innodb_flush_log_at_trx_commit = 2;       -- 日志刷新策略(性能优化)
SET GLOBAL innodb_file_per_table = 1;                -- 每表一个文件
SET GLOBAL innodb_flush_method = 'O_DIRECT';         -- 刷新方法
SET GLOBAL innodb_log_file_size = 268435456;         -- 日志文件大小(256MB)
SET GLOBAL innodb_thread_concurrency = 16;           -- 线程并发数
SET GLOBAL innodb_read_io_threads = 8;               -- 读IO线程数
SET GLOBAL innodb_write_io_threads = 8;              -- 写IO线程数

-- 临时表配置
SET GLOBAL tmp_table_size = 134217728;               -- 临时表大小(128MB)
SET GLOBAL max_heap_table_size = 134217728;          -- 堆表大小(128MB)

-- 线程配置
SET GLOBAL thread_cache_size = 50;                   -- 线程缓存大小
SET GLOBAL table_open_cache = 2000;                  -- 表缓存大小

-- 慢查询日志
SET GLOBAL slow_query_log = 1;                       -- 启用慢查询日志
SET GLOBAL long_query_time = 2;                      -- 慢查询时间阈值(2秒)
SET GLOBAL log_queries_not_using_indexes = 1;        -- 记录未使用索引的查询

-- 二进制日志配置
SET GLOBAL expire_logs_days = 7;                     -- 日志保留天数
SET GLOBAL max_binlog_size = 1073741824;             -- 二进制日志最大大小(1GB)

-- 字符集配置
SET GLOBAL character_set_server = 'utf8mb4';         -- 服务器字符集
SET GLOBAL collation_server = 'utf8mb4_unicode_ci';  -- 服务器排序规则

-- 显示当前配置
SELECT 
    'max_connections' as setting_name, 
    @@max_connections as current_value,
    '最大连接数' as description
UNION ALL
SELECT 
    'innodb_buffer_pool_size', 
    @@innodb_buffer_pool_size,
    'InnoDB缓冲池大小'
UNION ALL
SELECT 
    'query_cache_size', 
    @@query_cache_size,
    '查询缓存大小'
UNION ALL
SELECT 
    'wait_timeout', 
    @@wait_timeout,
    '等待超时时间'
UNION ALL
SELECT 
    'thread_cache_size', 
    @@thread_cache_size,
    '线程缓存大小';

-- 检查当前连接状态
SELECT 
    'Current_connections' as metric,
    VARIABLE_VALUE as value
FROM information_schema.GLOBAL_STATUS 
WHERE VARIABLE_NAME = 'Threads_connected'
UNION ALL
SELECT 
    'Max_used_connections',
    VARIABLE_VALUE
FROM information_schema.GLOBAL_STATUS 
WHERE VARIABLE_NAME = 'Max_used_connections'
UNION ALL
SELECT 
    'Connection_usage_ratio',
    CONCAT(ROUND((SELECT VARIABLE_VALUE FROM information_schema.GLOBAL_STATUS WHERE VARIABLE_NAME = 'Threads_connected') / @@max_connections * 100, 2), '%')
UNION ALL
SELECT 
    'Query_cache_hit_rate',
    CONCAT(ROUND(
        (SELECT VARIABLE_VALUE FROM information_schema.GLOBAL_STATUS WHERE VARIABLE_NAME = 'Qcache_hits') / 
        ((SELECT VARIABLE_VALUE FROM information_schema.GLOBAL_STATUS WHERE VARIABLE_NAME = 'Qcache_hits') + 
         (SELECT VARIABLE_VALUE FROM information_schema.GLOBAL_STATUS WHERE VARIABLE_NAME = 'Com_select')) * 100, 2
    ), '%');

-- 创建性能监控视图
CREATE OR REPLACE VIEW performance_summary AS
SELECT 
    'Connections' as category,
    'Current' as metric,
    (SELECT VARIABLE_VALUE FROM information_schema.GLOBAL_STATUS WHERE VARIABLE_NAME = 'Threads_connected') as value
UNION ALL
SELECT 
    'Connections',
    'Max_used',
    (SELECT VARIABLE_VALUE FROM information_schema.GLOBAL_STATUS WHERE VARIABLE_NAME = 'Max_used_connections')
UNION ALL
SELECT 
    'Connections',
    'Max_allowed',
    @@max_connections
UNION ALL
SELECT 
    'Buffer_Pool',
    'Size_MB',
    ROUND(@@innodb_buffer_pool_size / 1024 / 1024, 0)
UNION ALL
SELECT 
    'Query_Cache',
    'Size_MB',
    ROUND(@@query_cache_size / 1024 / 1024, 0)
UNION ALL
SELECT 
    'Slow_Queries',
    'Count',
    (SELECT VARIABLE_VALUE FROM information_schema.GLOBAL_STATUS WHERE VARIABLE_NAME = 'Slow_queries');

-- 创建连接监控存储过程
DELIMITER //
CREATE PROCEDURE monitor_connections()
BEGIN
    DECLARE current_conn INT;
    DECLARE max_conn INT;
    DECLARE usage_percent DECIMAL(5,2);
    
    SELECT VARIABLE_VALUE INTO current_conn 
    FROM information_schema.GLOBAL_STATUS 
    WHERE VARIABLE_NAME = 'Threads_connected';
    
    SELECT @@max_connections INTO max_conn;
    
    SET usage_percent = (current_conn / max_conn) * 100;
    
    SELECT 
        current_conn as current_connections,
        max_conn as max_connections,
        usage_percent as usage_percentage,
        CASE 
            WHEN usage_percent > 80 THEN 'WARNING: High connection usage'
            WHEN usage_percent > 90 THEN 'CRITICAL: Very high connection usage'
            ELSE 'OK'
        END as status;
END //
DELIMITER ;

-- 创建索引优化建议查询
SELECT 
    table_schema,
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as table_size_mb,
    ROUND((data_length / 1024 / 1024), 2) as data_size_mb,
    ROUND((index_length / 1024 / 1024), 2) as index_size_mb,
    ROUND((index_length / data_length), 2) as index_ratio
FROM information_schema.tables 
WHERE table_schema NOT IN ('information_schema', 'mysql', 'performance_schema', 'sys')
    AND table_type = 'BASE TABLE'
    AND data_length > 0
ORDER BY (data_length + index_length) DESC
LIMIT 20;

-- 显示当前配置摘要
SELECT 'MySQL Performance Optimization Applied' as status;
SELECT 'Run: CALL monitor_connections(); to check connection status' as next_step;
SELECT 'Run: SELECT * FROM performance_summary; to view performance metrics' as monitoring;
