# Redis优化配置文件
# 将此配置添加到redis.conf或创建新的配置文件

# 基本配置
port 6379
bind 127.0.0.1
protected-mode yes
tcp-backlog 511
timeout 300
tcp-keepalive 300

# 内存配置
maxmemory 2gb                           # 最大内存使用(根据服务器内存调整)
maxmemory-policy allkeys-lru            # 内存淘汰策略
maxmemory-samples 5                     # LRU样本数

# 持久化配置
save 900 1                              # 900秒内至少1个key变化时保存
save 300 10                             # 300秒内至少10个key变化时保存
save 60 10000                           # 60秒内至少10000个key变化时保存

stop-writes-on-bgsave-error yes         # 后台保存错误时停止写入
rdbcompression yes                      # RDB压缩
rdbchecksum yes                         # RDB校验和
dbfilename dump.rdb                     # RDB文件名
dir /var/lib/redis                      # 数据目录

# AOF配置
appendonly yes                          # 启用AOF
appendfilename "appendonly.aof"         # AOF文件名
appendfsync everysec                    # 每秒同步
no-appendfsync-on-rewrite no           # 重写时不同步
auto-aof-rewrite-percentage 100        # AOF重写百分比
auto-aof-rewrite-min-size 64mb         # AOF重写最小大小

# 网络配置
tcp-backlog 511                         # TCP监听队列大小
timeout 0                               # 客户端空闲超时(0=禁用)
tcp-keepalive 300                       # TCP保活时间

# 客户端配置
maxclients 10000                        # 最大客户端连接数

# 慢日志配置
slowlog-log-slower-than 10000           # 慢查询阈值(微秒)
slowlog-max-len 128                     # 慢查询日志最大长度

# 延迟监控
latency-monitor-threshold 100           # 延迟监控阈值(毫秒)

# 通知配置
notify-keyspace-events ""               # 键空间通知

# 高级配置
hash-max-ziplist-entries 512            # 哈希压缩列表最大条目
hash-max-ziplist-value 64               # 哈希压缩列表最大值
list-max-ziplist-size -2                # 列表压缩大小
list-compress-depth 0                   # 列表压缩深度
set-max-intset-entries 512              # 集合整数集最大条目
zset-max-ziplist-entries 128            # 有序集合压缩列表最大条目
zset-max-ziplist-value 64               # 有序集合压缩列表最大值
hll-sparse-max-bytes 3000               # HyperLogLog稀疏最大字节

# 活跃重哈希
activerehashing yes                     # 启用活跃重哈希

# 客户端输出缓冲区限制
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# 客户端查询缓冲区限制
client-query-buffer-limit 1gb

# 协议最大批量请求大小
proto-max-bulk-len 512mb

# 频率配置
hz 10                                   # 后台任务频率

# 动态HZ
dynamic-hz yes                          # 启用动态HZ

# AOF重写增量fsync
aof-rewrite-incremental-fsync yes

# RDB保存增量fsync
rdb-save-incremental-fsync yes

# LFU配置
lfu-log-factor 10                       # LFU日志因子
lfu-decay-time 1                        # LFU衰减时间

# 内存使用优化
# 针对游戏服务器的特殊配置

# 数据库分离配置说明:
# DB 0: 主缓存数据(用户数据、游戏状态等)
# DB 1: 会话数据(登录状态、临时数据等)
# DB 2: 统计数据(分析数据、日志等)
# DB 3: 配置缓存(游戏配置、系统配置等)

# 安全配置
# requirepass your_redis_password        # 设置密码(取消注释并设置强密码)
# rename-command FLUSHDB ""              # 禁用危险命令
# rename-command FLUSHALL ""
# rename-command KEYS ""
# rename-command CONFIG ""

# 日志配置
loglevel notice                         # 日志级别
logfile /var/log/redis/redis-server.log # 日志文件
syslog-enabled no                       # 系统日志

# 监控配置
# 可以通过以下命令监控Redis性能:
# redis-cli --latency-history
# redis-cli --stat
# redis-cli info memory
# redis-cli info clients
# redis-cli info stats

# 性能调优建议:
# 1. 根据服务器内存调整maxmemory
# 2. 监控内存使用情况，避免swap
# 3. 定期检查慢查询日志
# 4. 使用pipeline批量操作
# 5. 合理设置过期时间
# 6. 避免使用KEYS命令，使用SCAN替代
# 7. 监控连接数，避免连接泄漏

# 集群配置(如果需要)
# cluster-enabled yes
# cluster-config-file nodes-6379.conf
# cluster-node-timeout 15000
# cluster-announce-ip 127.0.0.1
# cluster-announce-port 6379
# cluster-announce-bus-port 16379

# 主从复制配置(如果需要)
# replicaof <masterip> <masterport>
# masterauth <master-password>
# replica-serve-stale-data yes
# replica-read-only yes
# repl-diskless-sync no
# repl-diskless-sync-delay 5
