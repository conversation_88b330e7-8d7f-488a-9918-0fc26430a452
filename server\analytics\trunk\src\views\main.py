#-*- coding: utf-8 -*-
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import render_to_response
from django.template import RequestContext
from django.conf import settings
import datetime, time
import db
from db import db_server
from common import utils
from common.pager import Pager
from sqlalchemy import *
import hashlib
ana_key = 'a&(B5!59e#2D0c$a813%be^aC&71'

def judge():
    try:
        now = datetime.datetime.now()
        if now.date() != db.reload_time.date():
            reload(db)
            print '******************db reloading******************'
    except:
        print utils.get_err()

def operation(request):
    try:
        uid = request.REQUEST.get('uid', None)
        name = request.REQUEST.get('name', None)
        zone = request.REQUEST.get('zone', None)
        pf = request.REQUEST.get('pf', None)
        #session_key = request.REQUEST.get('session_key')
        if not uid or not zone or not name:
            return HttpResponse('The uid or zone must be required')
        #local_session_key = hashlib.md5(uid+zone+ana_key).hexdigest()
        #if local_session_key != session_key:
        #    return HttpResponse('Session Error')

        user =  {'uid': uid, 'name': name, 'zone': zone, 'pf': pf}
        lv = request.REQUEST.get('lv_up', None)
        if lv:
            ana_data = {}
            lv_list = lv.split('|')
            ana_data[str(lv_list[0])] = -1
            ana_data[str(lv_list[1])] = 1
            save_operation(ana_data, user, '0', 'user_lv')
            save_operation(ana_data, user, zone, 'user_lv')

        #获得的单位
        eids = request.REQUEST.get('eids', None)
        if eids:
            ana_data = {}
            for item in eids.split('|'):
                k,v = item.split('-')
                if ana_data.has_key(k):
                    ana_data[k] += int(v)
                else:
                    ana_data[k] = int(v)
            save_operation(ana_data, user, '0', 'equip')
            save_operation(ana_data, user, zone, 'equip')

        #获得的装备魂
        s_eids = request.REQUEST.get('s_eids', None)

        if s_eids:
            ana_data = {}
            for item in s_eids.split('|'):
                k,v = item.split('-')
                if ana_data.has_key(k):
                    ana_data[k] += int(v)
                else:
                    ana_data[k] = int(v)
            save_operation(ana_data, user, '0', 'equip_soul')
            save_operation(ana_data, user, zone, 'equip_soul')


        #获得的单位
        aids = request.REQUEST.get('aids', None)
        if aids:
            ana_data = {}
            for item in aids.split('|'):
                k,v = item.split('-')
                if ana_data.has_key(k):
                    ana_data[k] += int(v)
                else:
                    ana_data[k] = int(v)
            save_operation(ana_data, user, '0', 'avatar')
            save_operation(ana_data, user, zone, 'avatar')


        #获得的单位魂
        s_aids = request.REQUEST.get('s_aids', None)

        if s_aids:
            ana_data = {}
            for item in s_aids.split('|'):
                k,v = item.split('-')
                if ana_data.has_key(k):
                    ana_data[k] += int(v)
                else:
                    ana_data[k] = int(v)
            save_operation(ana_data, user, '0', 'avatar_soul')
            save_operation(ana_data, user, zone, 'avatar_soul')


        #升阶的单位
        aids = request.REQUEST.get('phase_up', None)
        if aids:
            ana_data = {}
            for item in aids.split('|'):
                ana_data[item.split('-')[0]] = int(item.split('-')[1])
            save_operation(ana_data, user, '0', 'phase_up')
            save_operation(ana_data, user, zone, 'phase_up')

        #升星的单位
        aids = request.REQUEST.get('star_up', None)
        if aids:
            ana_data = {}
            for item in aids.split('|'):
                ana_data[item.split('-')[0]] = int(item.split('-')[1])
            save_operation(ana_data, user, '0', 'star_up')
            save_operation(ana_data, user, zone, 'star_up')

        #过关所使用的单位
        team = request.REQUEST.get('team', None)
        if team:
            ana_data = {}
            for item in team.split('|'):
                ana_data[item.split('-')[0]] = int(item.split('-')[1])
            save_operation(ana_data, user, '0', 'team')
            save_operation(ana_data, user, zone, 'team')
        #胜利的主线精英关卡id
        hid = request.REQUEST.get('hid', None)
        if hid:
            ana_data = {hid: 1}
            save_operation(ana_data, user, '0', 'combat_hard')
            save_operation(ana_data, user, zone, 'combat_hard')
            
        #胜利的主线关卡id
        cid = request.REQUEST.get('cid', None)
        if cid:
            ana_data = {cid: 1}
            save_operation(ana_data, user, '0', 'combat_main')
            save_operation(ana_data, user, zone, 'combat_main')

        #失败的主线精英关卡id
        lose_hid = request.REQUEST.get('lose_hid', None)
        if lose_hid:
            ana_data = {lose_hid: 1}
            save_operation(ana_data, user, '0', 'combat_hard_lose')
            save_operation(ana_data, user, zone, 'combat_hard_lose')

        #失败的主线关卡id
        lose_cid = request.REQUEST.get('lose_cid', None)
        if lose_cid:
            ana_data = {lose_cid: 1}
            save_operation(ana_data, user, '0', 'combat_main_lose')
            save_operation(ana_data, user, zone, 'combat_main_lose')

        #胜利的活动关卡id
        pid = request.REQUEST.get('pid', None)
        if pid:
            ana_data = {pid: 1}
            save_operation(ana_data, user, '0', 'combat_ploy')
            save_operation(ana_data, user, zone, 'combat_ploy')

        #失败的活动关卡id
        lose_pid = request.REQUEST.get('lose_pid', None)
        if lose_pid:
            ana_data = {lose_pid: 1}
            save_operation(ana_data, user, '0', 'combat_ploy_lose')
            save_operation(ana_data, user, zone, 'combat_ploy_lose')
        
        #新手无操作记录
        def _no_operation(zone):
            table = get_user_table(uid, zone)
            now = datetime.datetime.now()
            user_res = table.find_one({'uid': uid})
            if user_res and 'first_day_operation' in user_res:
                user_res['now'] = now
                no_operation_handler(user_res, zone, save=True, pf=pf)
        _no_operation('0')
        _no_operation(zone)

        #炼金的装备
        eids = request.REQUEST.get('equip_alchemy', None)
        if eids:
            ana_data = {}
            for item in eids.split('|'):
                ana_data[item.split('-')[0]] = int(item.split('-')[1])
            save_operation(ana_data, user, '0', 'equip_alchemy')
            save_operation(ana_data, user, zone, 'equip_alchemy')

    except Exception,e:
        print e

    return HttpResponse('ok')


def save_operation(ana_data, user, zone, ana_type):
    _save_operation(ana_data, user, zone, ana_type)
    if user['pf']:
        _save_operation(ana_data, user, zone, ana_type, user['pf'])
    
def _save_operation(ana_data, user, zone, ana_type, pf=None):
    current_time = datetime.datetime.now()
    now_key = 'now|%s' % ana_type
    db_engine = db_server.get_dbengine(zone)
    if pf is None:
        table_name = 'operation'
    else:
        table_name = 'operation_'+pf
    ana_obj = db_engine[table_name].find_one({'key': now_key})
    data = {}
    for k,v in ana_data.items():
        data[k] = {
                'pass_num': v,
                'user': {'uid': user['uid'], 'name': user['name']},
                }
    if ana_obj:
        if current_time.date() != ana_obj['subtime'].date():
            back_date_key = '%s|%s' % (str((current_time-datetime.timedelta(days=1)).date()),ana_type)
            del ana_obj['_id']
            ana_obj['key'] = back_date_key
            ana_obj['subtime'] = current_time
            db_engine[table_name].insert(ana_obj)
        for k,v in data.items():
            if ana_obj.has_key(k):
                if v['pass_num'] < 0:
                    data[k]['user'] = ana_obj[k]['user']
                data[k]['pass_num'] += ana_obj[k]['pass_num']

        data['subtime'] = current_time
        db_engine[table_name].update({'key': now_key},{'$set': data})

    else:
        data.update({'key': now_key, 'subtime': current_time})
        db_engine[table_name].insert(data)

def index(request):
    def _ins_data(uid, zone, lv, vip, pf):
        user_res = get_user_res(uid, zone, lv, vip)
        user_res = user_res_handle(user_res)
        keep_back_handle(user_res, zone, pf)
        dau_online_handle(user_res, zone, pf)
        no_operation_handler(user_res, zone, False, pf)
        wau_mau_handle('wau', user_res, zone, pf)
        wau_mau_handle('mau', user_res, zone, pf)
        save_user_res(user_res, zone, pf)
    judge()
    #session_key = request.REQUEST.get('session_key')
    try:
        uid = request.REQUEST.get('uid')
        zone = request.REQUEST.get('zone')
        pf = request.REQUEST.get('pf')
        lv = request.REQUEST.get('lv')
        vip = request.REQUEST.get('vip_lv')
        if not uid or not zone or not lv or not vip:
            return HttpResponse('The uid or zone must be required')
        zone = str(zone)
        #if zone not in settings.ZONES.keys():
        #    return HttpResponse('The uid or zone must be required')
        #local_session_key = hashlib.md5(uid+zone+ana_key).hexdigest()
        #if local_session_key != session_key:
        #    return HttpResponse('Session Error')
        _ins_data(uid, '0', lv, vip, pf)
        _ins_data(uid, zone, lv, vip, pf)
    except Exception,e:
        print '##############################'
        print e


    return HttpResponse('ok')

def wau_mau_handle(table_format ,user_res, zone, pf):
    if user_res['now'] != user_res['login_times'][-1]:
        return
    if table_format == 'wau':
        days_len = 7
    elif table_format == 'mau':
        days_len = 30
    else:
        raise
    login_times = user_res['login_times']
    days = range(days_len)
    if len(login_times) != 1:
        c = (user_res['login_times'][-1].date()-user_res['login_times'][-2].date()).days
        if c > days_len:
            c = days_len
        days = days[days_len-c:]

    mod_days = {}
    for item in days:
        mod_day = user_res['now'].date() + datetime.timedelta(days=item)
        mod_ym = str(mod_day)[:7]
        if not mod_days.has_key(mod_ym):
            mod_days[mod_ym] = {'%s.%s' % (str(mod_day), 'lv_%s' % str(user_res['lv'])):1}
            mod_days[mod_ym]['%s.%s' % (str(mod_day), 'vip_%s' % str(user_res['vip']))]=1
        else:
            #mod_days[mod_ym][str(mod_day)]=1
            mod_days[mod_ym]['%s.%s' % (str(mod_day), 'lv_%s' % str(user_res['lv']))]=1
            mod_days[mod_ym]['%s.%s' % (str(mod_day), 'vip_%s' % str(user_res['vip']))]=1
    db_engine = db_server.get_dbengine(zone)
    for k,v in mod_days.items():
        db_engine[table_format].update({table_format+'_mon': k}, {'$inc': v}, upsert=True)
        if pf:
            db_engine[table_format+'_'+pf].update({table_format+'_mon': k}, {'$inc': v}, upsert=True)
    

#计算一个时间在一天中处于哪个时间段
def get_dt_step(dt):
    interval = 30  #minutes,分段单位
    hour = dt.hour
    minute = dt.minute
    bdt = datetime.datetime(*dt.timetuple()[:3])
    minutes_total = hour*60+minute
    b = minutes_total/interval
    minutes = interval*b
    dt = bdt+datetime.timedelta(minutes=minutes)
    return '%02d%02d' % (dt.hour,dt.minute)

def dau_online_handle(user_res, zone, pf):
    dau_online_dict = {}
    now_step = get_dt_step(user_res['now'])
    if user_res['now'] == user_res['login_times'][-1]:
        #当天第一次登录，包括首次安装
        dau_online_dict['dau.%s' % 'lv_'+str(user_res['lv'])] = 1
        dau_online_dict['dau.%s' % 'vip_'+str(user_res['vip'])] = 1
        #dau_online_dict['dau'] = 1
        dau_online_dict[now_step] = 1
    else:
        #非当天首次登录，需要计算上次登录时间段
        last_login_step = get_dt_step(user_res['login_time'])
        if now_step != last_login_step:
            dau_online_dict[now_step] = 1
    db_engine = db_server.get_dbengine(zone)
    if dau_online_dict:
        db_engine['dau_online'].update({'odate': str(user_res['now'].date())}, {'$inc': dau_online_dict}, upsert=True)
        if pf:
            db_engine['dau_online_'+pf].update({'odate': str(user_res['now'].date())}, {'$inc': dau_online_dict}, upsert=True)

def user_total_handle(now_time , zone, pf):
    db_engine = db_server.get_dbengine(zone)
    bak = False
    total_res = db_engine['keep_back'].find_one({'kdate': 'user_total'})
    db_engine['keep_back'].update({'kdate': 'user_total'}, {'$inc': {'num': 1}, '$set':{'tt': now_time}},upsert=True)
    if total_res:
        if now_time.date() != total_res['tt'].date():
            bak_date = str(total_res['tt'].date())
            db_engine['keep_back'].update({'kdate': 'utotal_' + bak_date}, {'$set': {'num': total_res['num'], 'tt': total_res['tt']}},upsert=True)

    if pf:
        total_res = db_engine['keep_back_'+pf].find_one({'kdate': 'user_total'})
        db_engine['keep_back_'+pf].update({'kdate': 'user_total'}, {'$inc': {'num': 1}, '$set': {'tt': now_time}},upsert=True)
        if total_res:
            if now_time.date() != total_res['tt'].date():
                bak_date = str(total_res['tt'].date())
                db_engine['keep_back_' + pf].update({'kdate': 'utotal_' + bak_date}, {'$set': {'$set': {'num': total_res['num'], 'tt': total_res['tt']}}},upsert=True)


        


def keep_back_handle(user_res, zone, pf):
    if user_res['now'] != user_res['login_times'][-1]:
        return
    back_list = [0,1,2,3,4,5,6,7,14,30,60,90]
    back_day = (user_res['login_times'][-1].date()-user_res['add_time'].date()).days
    if (user_res['login_times'][-1].date()-user_res['add_time'].date()).days not in back_list:
        return
    back_dict = {'back_%d'%back_day: 1}
    db_engine = db_server.get_dbengine(zone)
    db_engine['keep_back'].update({'kdate': str(user_res['add_time'].date())}, {'$inc': back_dict}, upsert=True)
    if pf:
        db_engine['keep_back_'+pf].update({'kdate': str(user_res['add_time'].date())}, {'$inc': back_dict}, upsert=True)

    if back_day == 0:
        user_total_handle(user_res['now'], zone, pf)

def no_operation_handler(user_res, zone, save=False, pf=None):
    if (user_res['add_time'].date()==user_res['now'].date() #今日安装
            and user_res['add_time']!=user_res['now']    #非首次登录   
            and user_res['first_day_operation']==False): 
        user_res['first_day_operation'] = True
        db_engine = db_server.get_dbengine(zone)
        back_dict = {'operation': 1} #记录今日安装有操作的人数
        db_engine['keep_back'].update({'kdate': str(user_res['add_time'].date())}, {'$inc': back_dict}, upsert=True)
        if pf:
            db_engine['keep_back_'+pf].update({'kdate': str(user_res['add_time'].date())}, {'$inc': back_dict}, upsert=True)
        if save:
            table = get_user_table(user_res['uid'], zone)
            table.update({'uid': user_res['uid']}, {'$set': {'first_day_operation': user_res['first_day_operation']}})

def user_res_handle(user_res):
    login_times = user_res['login_times']
    if login_times[-1].date() != user_res['now'].date():
        login_times.append(user_res['now'])
        length = len(login_times)
        for i in xrange(length):
            if login_times[i] >= user_res['now']-datetime.timedelta(days=30):
                login_times = login_times[i:]
                break
        user_res['login_times'] = login_times
    return user_res

def get_user_table(uid, zone):
    length = len(uid) 
    str_num = 0
    for i in range(0, length):
        str_num += ord(uid[i]) * (256**(length -i -1))
    table_index = str_num % db_server.user_table_num
    table_format = 'user_%d' % table_index
    db_engine = db_server.get_dbengine(zone)
    return db_engine[table_format]

def get_user_res(uid, zone, lv, vip, t = None):
    # 多线程安全优化 - 初始化res变量，防止数据库异常时未赋值
    res = None
    now = datetime.datetime.now()
    if t:
        now = t

    try:
        table = get_user_table(uid, zone)
        res = table.find_one({'uid': uid})
    except Exception as e:
        # 数据库连接异常时，记录错误但不中断服务
        print("Database error in get_user_res: %s" % str(e))
        res = None

    # 如果数据库查询失败或用户不存在，创建新用户数据
    if not res:
        res = {
                'uid': uid,
                'lv': lv,
                'vip': vip,
                'add_time': now,
                'first_day_operation': False,
                'login_time': now,
                'login_times': [now],
                }

    # 确保res已正确初始化后再进行后续操作
    if res:
        res['now'] = now
        if res['lv'] != lv:
            res['lv'] = lv
        if res['vip'] != vip:
            res['vip'] = vip
        if 'first_day_operation' not in res:
            res['first_day_operation'] = False

    return res

def save_user_res(user_res, zone, pf):
    table = get_user_table(user_res['uid'], zone)
    if user_res['add_time'] == user_res['now']:
        del user_res['now']
        table.insert(user_res)
        if zone != '0':
            save_operation({'2': 1}, {'uid': user_res['uid'], 'name': '', 'pf': pf},  zone, 'user_lv')
            save_operation({'2': 1}, {'uid': user_res['uid'], 'name': '', 'pf': pf},  '0', 'user_lv')
    else:
        update_res = {'login_time': user_res['now'], 'first_day_operation': user_res['first_day_operation']}
        if user_res['now'] == user_res['login_times'][-1]:
            update_res['login_times'] = user_res['login_times']
        table.update({'uid': user_res['uid']}, {'$set': update_res})

#################################################
class ApiError(Exception): pass

def check_session(request):
    uid = request.REQUEST.get('uid')
    zone = request.REQUEST.get('zone')
    pf = request.REQUEST.get('pf')
    lv = request.REQUEST.get('lv')
    #session_key = request.REQUEST.get('session_key')

    if not uid or not zone or not pf or not lv:
        raise ApiError('The uid/zone/pf/lv must be required')
    #if hashlib.md5(uid+zone+ana_key).hexdigest()!=session_key:
    #    raise ApiError('Session Error')

    db_engine = db_server.get_dbengine()
    now = datetime.datetime.now()
    return uid, zone, pf, lv, db_engine, now

def get_coin(request):
    try:
        uid, zone, pf, lv, db_engine, now = check_session(request)
    except ApiError, e:
        return HttpResponse(e.message, status=500)

    gtype = request.REQUEST.get('gtype')
    num = request.REQUEST.get('num')
    if not gtype or not num:
        return HttpResponse('type/num required', status=500)
    num = int(num)
    data = { 'uid': uid, 'zone': zone, 'pf': pf, 'lv': lv, 'add_time': now, 
            'type': gtype, 'num': num
            }
    db_engine['get_coin_log'].insert(data)
    return HttpResponse('ok')

def consume_good(request):
    try:
        uid, zone, pf, lv, db_engine, now = check_session(request)
    except ApiError, e:
        return HttpResponse(e.message, status=500)

    aid = request.REQUEST.get('aid')
    ctype = request.REQUEST.get('ctype')
    if not aid or not ctype:
        return HttpResponse('aid required', status=500)

    data = { 'uid': uid, 'zone': zone, 'pf': pf, 'lv': lv, 'add_time': now, 
            'aid': aid,'ctype': ctype,
            }
    db_engine['consume_good_log'].insert(data)
    return HttpResponse('ok')



