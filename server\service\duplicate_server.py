#-*- coding: utf-8 -*-
import os, sys, time, datetime, random, json, struct
from functools import partial
from tornado import web, websocket, gen, ioloop, autoreload, httpserver
from tornado.options import parse_command_line, options
from tornado.log import app_log
import c<PERSON>ickle as pickle
import random, uuid
import operator
from copy import deepcopy as copy
from model import Model, Model_Error
from jsonrpc.proxy import ServiceProxy
from jsonrpc.proxy2 import ServiceProxy as ServiceProxy2
from common import utils
import math
import re
import urllib2
import urllib
from hashlib import md5
from tornado.httpclient import AsyncHTTPClient
from concurrent.futures import ThreadPoolExecutor
from tornado.concurrent import run_on_executor

AsyncHTTPClient.configure("tornado.curl_httpclient.CurlAsyncHTTPClient")

sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../', 'trunk/llol/src'))
import settings
from decimal import Decimal
import gc as ggc
import zlib
ggc.disable()



class Game_config(object):
    def __init__(self):
        self.data = {}

    def __getattr__(self, key):
        return self.data[key]

game_config = Game_config()


def json_default(obj):
    if isinstance(obj, datetime.datetime):
        return {"$datetime": obj.strftime('%Y-%m-%d %H:%M:%S')}
    if isinstance(obj, datetime.date):
        return {"$date": obj.strftime('%Y-%m-%d')}
def json_object_hook(dct):
    if "$datetime" in dct:
        return datetime.datetime.strptime(dct['$datetime'], '%Y-%m-%d %H:%M:%S')
    if "$date" in dct:
        return datetime.date(*map(int, (dct["$date"].split('-'))))
    return dct

WHERE = settings.WHERE
CACHE_PRE = settings.CACHE_PRE
_wapi = ServiceProxy(settings.BASE2_URL[0])
wapi = ServiceProxy2(settings.BASE2_URL)

class City(Model):
    seq_attrs = ['cid', 'country', 'floor', 'monster', 'monster_num',
            'troop_ids', 'fight','occupy_time']

    def __init__(self):
        self.cid = None
        self.floor = None
        self.monster = None
        self.monster_num = 0
        self.country = None
        self.troop_ids = []
        self.fight = None
        self.occupy_time = [int(time.time()),0]

    def get_defend_troops(self):
        troops = []
        for troop_id in self.troop_ids:
            uid, hid = troop_id.split('_')
            uid = int(uid)
            if User.users[uid].country != self.country:
                continue
            troop = world.troops[uid][hid]
            if troop['status'] != 0:
                continue
            #傲气初始化为0准备开战
            troop = copy(troop)
            troop['proud'] = 0
            if troop.has_key('data'):
                del troop['data']
            troops.append(troop)
        return troops

    def push_follow_fight(self, method, push_data):
        for uid in self.fight['follow_uids']:
            User.users[uid].write(method, push_data)

    def get_small_city_dumps(self):
        city_dumps = self.dumps()
        if city_dumps['fight']:
            last_battle_result = None
            if city_dumps['fight'].get('last_battle_result',None):
                last_battle_result = 1
            city_dumps['fight'] = {
                    'country_logs': city_dumps['fight']['country_logs'],
                    'team_len': [len(city_dumps['fight']['team'][0]['troop']), len(city_dumps['fight']['team'][1]['troop'])],
                    'fireCountry': city_dumps['fight']['fireCountry'],
                    }
            if last_battle_result:
                city_dumps['fight']['last_battle_result'] = 1
        return city_dumps


class World(Model):
    seq_attrs = ['open_date','duplicate_time','status', 'cities','troops','group_id','level','energy_num','recovery_num', 'country_buff','log_index','fight_logs','win_country','win_type','win_country','if_refund','user_move_times','country_leader']



    def __init__(self):
        self.open_date = None
        self.duplicate_time = []
        self.status = 0
        self.group_id = None
        self.level = None
        self.energy_num = 1
        self.recovery_num = 1

        self.troops = {}
        self.cities = {}
        self.country_buff = {}
        self.country_leader = {}
        self.log_index = 0
        self.fight_logs= {}
        self.win_type = None
        self.win_country = None
        self.if_refund = False
        self.user_move_times = 0


    @gen.coroutine
    def check_server_status(self, now_d, has_run, has_fight):
        now_date = now_d.date()
        #status = 0 等待初始化 
        #status = 1 初始化完成等待进场
        #status = 2 进场等待开战
        #status = 3 开战定时器启动
        #status = 4 到时间判断灭火是否结算
        #status = 5 结算发放d_score,排行榜奖励
        #status = 6 结算发放未兑换的棋子
        #status = 7 战场已关闭
        if self.open_date != now_date:
            raise gen.Return(False)
        if self.status >= 7:
            raise gen.Return(False)
        if self.status == 0:   #等待初始化
            raise gen.Return(False)
        elif self.status == 1: #等待进场
            if now_d < self.duplicate_time[1]:
                raise gen.Return(False)
            self.status = 2
        elif self.status == 2: #进场不开战
            #选举指挥
            if now_d >= self.duplicate_time[1] + datetime.timedelta(minutes=game_config.duplicate['campaign_time']):
                self.set_country_leader()
            if now_d < self.duplicate_time[2]:
                raise gen.Return(False)
            self.status = 3
        elif self.status == 3: #开战未结算
            if now_d < self.duplicate_time[3]:
                raise gen.Return(False)
            self.status = 4
        elif self.status == 4: #到时间结算
            if not has_run and not has_fight:
                self.status = 5
                self.win_type = 1  #靠格子数量取胜
                #chu,han = sorted(duplicate_config['win'].keys())
                chu,han = sorted(self.country_buff.keys())
                country_city_nums = {
                        chu:0,
                        han:0,
                        }
                for city in self.cities.values():
                    if country_city_nums.has_key(city.country):
                        country_city_nums[city.country] += 1
                if country_city_nums[chu] == country_city_nums[han]:
                    self.win_country = -1
                else:
                    if country_city_nums[chu] > country_city_nums[han]:
                        self.win_country = chu
                    else:
                        self.win_country = han
        elif self.status == 5: #结算完成发d_score
            self.push_all_user('dw.cross_service_finish', {'win_country': self.win_country, 'win_type': self.win_type, 'status': self.status})
            rank_dict = {}
            for item in  User().get_k_score_rank({}):
                rank_dict[item['uid']] = item['rank']
            for user in User.users.values():
                rank = rank_dict.get(user.uid,0)
                yield user.push_d_score(rank)
                user.settle_prestige()
            self.status = 6


        elif self.status == 6: #关闭并发邮件奖励
            if now_d < self.duplicate_time[4]:
                raise gen.Return(False)
            for user in User.users.values():
                yield user.push_duplicate_over()
            self.status = 7
            #self.backup_duplicate_to_db()
            ggc.collect()


    def backup_duplicate_to_db(self):
        from database_backup import DB
        from sqlalchemy import text
        conn = DB.connect()
        try:

            world_dict = {}
            for attr in self.seq_attrs:
                if attr == 'cities':
                    world_dict[attr] = {}
                    for cid,city in self.cities.items():
                        world_dict[attr][cid] = city.dumps()
                else:
                    world_dict[attr] = getattr(self, attr)
            user_dict = {}
            for user in User.users.values():
                user_dict[user.uid] = user.dumps()

            open_date = int(str(self.open_date).replace('-',''))
            dnum_date = '%s|%s' % (options.dnum,open_date)
            world_res = pickle.dumps(world_dict,-1)
            world_res = zlib.compress(world_res)
            user_res = pickle.dumps(user_dict,-1)
            user_res = zlib.compress(user_res)
            
            db_dict = {
                    'dnum_date': dnum_date,
                    'group_id': self.group_id,
                    'open_date': open_date,
                    'level': self.level,
                    'dnum': options.dnum,
                    'world': world_res,
                    'user': user_res,
                    }
            conn.execute(text("insert into duplicate_log (dnum_date, group_id, open_date,level,dnum,world,user) values (:dnum_date,:group_id,:open_date,:level,:dnum,:world,:user)"),db_dict)
        except:
            pass
        conn.close()

    def init_duplicate(self, data):

        open_date = data['open_date']
        if self.open_date == open_date and not data.get('if_force',False):
            return False

        self.__init__()
        User.users = {}
        User.chat_cache = []
        User.chat_log = []
        self.duplicate_time = data['duplicate_time']
        self.open_date = data['open_date']
        self.status = 0

        self.group_id = data['group_id']   #渠道
        self.level = data['level']   #段位

        u_list = data['u_list']

        duplicate_config = game_config.duplicate
        country_list = duplicate_config['grouping']['country'][:2]
        self.country_buff = {
                country_list[0][0]: {
                    'unum': 0,              
                    'buff': [0.0,0.0],              
                    'clear_monster': 0,                     #清除野怪数 
                    'capital': country_list[0][1],          #首都
                    'enemy': country_list[1][0],            #敌国
                    'enemy_capital': country_list[1][1],    #敌国首都
                    },
                country_list[1][0]: {
                    'unum': 0,              
                    'buff': [0.0,0.0],              
                    'clear_monster': 0,                     #清除野怪数 
                    'capital': country_list[1][1],          #首都
                    'enemy': country_list[0][0],            #敌国
                    'enemy_capital': country_list[0][1],    #敌国首都
                    },
                }
        self.country_leader = {
                country_list[0][0]: {
                    'leader': None,              
                    'uids': [],              
                    'cids': [],              
                    },
                country_list[1][0]: {
                    'leader': None,              
                    'uids': [],              
                    'cids': [],              
                    },
                }
        open_date = int(str(self.open_date).replace('-',''))
        if options.dnum%2 == 0:
            country_list = country_list[::-1]




        city_troop_ids = {}
        udid_list = []
        x = 0
        y = 1
        init_energy = duplicate_config['energy'][0]
        uname_dict = {}
        for u_dict in u_list:
            country_index = x%2
            country, cid = country_list[country_index]
            uid = u_dict['uid']
            city_troop_ids.setdefault(cid,[])
            self.country_buff[country]['unum'] += 1
            u_dict['country'] = country
            uname = u_dict['uname']
            uname_dict.setdefault(uname,0)
            uname_num = uname_dict[uname]
            if uname_num:
                u_dict['uname'] = '%s%s' % (uname,uname_num)
            uname_dict[uname] += 1
            #troop_data = u_dict['troop_data']

            for hid,h_data in u_dict['troop_data'].items():
                h_data['uid'] = u_dict['uid']
                h_data['country'] = u_dict['country']
                h_data['uname'] = u_dict['uname']
                h_data['head'] = u_dict['head']
                h_data['city'] = cid
                h_data['status'] = 0
                h_data['rush'] = 0
                h_data['data'] = None
                h_data['energy'] = init_energy
                h_data['energy_buy_times'] = 0
                h_data['revive_times'] = 0

                city_troop_ids[cid].append('%s_%s' % (uid, hid))
            self.troops[uid] = u_dict['troop_data']

            user = User.loads(u_dict)
            User.users[user.uid] = user
            udid_list.append(u_dict['udid'])
            


            y += 1
            if y%2==0:
                x += 1



        num_add = duplicate_config['monster']['num_add']


        for x in range(9):
            for y in range(9):
                cid = str(x*1000+y)
                city = City()
                city.cid = cid
                city.floor = duplicate_config['terrain'][y][x]
                #city.country = duplicate_config['floor'][city.floor]['country']
                city.country = duplicate_config['ascription_ini'][y][x]
                #if duplicate_config['win'].has_key(city.country):
                if self.country_buff.has_key(city.country):
                    #self.country_buff.setdefault(city.country,{'clear_monster':0,'buff':[0.0,0.0]})
                    dmg_all = duplicate_config['floor'][city.floor].get('dmg_all',0)
                    if dmg_all:
                        dmg_all = Decimal(str(dmg_all))
                        country_dmg_all = Decimal(str(self.country_buff[city.country]['buff'][0]))
                        country_dmg_all += dmg_all
                        self.country_buff[city.country]['buff'][0] = float(country_dmg_all)
                    res_all = duplicate_config['floor'][city.floor].get('res_all',0)
                    if res_all:
                        res_all = Decimal(str(res_all))
                        country_res_all = Decimal(str(self.country_buff[city.country]['buff'][1]))
                        country_res_all += res_all
                        self.country_buff[city.country]['buff'][1] = float(country_res_all)
                if city_troop_ids.has_key(cid):
                    city.troop_ids = city_troop_ids[cid]
                city.monster = duplicate_config['neutral'][y][x]
                if city.monster != 0:
                    monster_country = duplicate_config['ascription'][y][x]
                    monster_num = duplicate_config['monster'][city.monster]['num']
                    #if country_unum.has_key(monster_country):
                    monster_num = int(math.ceil(monster_num*min(num_add[2],(num_add[0]+num_add[1]*len(u_list)))))
                    city.monster_num = monster_num
                else:
                    city.monster_num = 0
                self.cities[cid] = city

        country_buff_items = self.country_buff.items()
        default_revive_seconds = duplicate_config['retreat_passive'][0]
        if country_buff_items[0][1]['unum'] != country_buff_items[1][1]['unum']:
            if country_buff_items[0][1]['unum'] < country_buff_items[1][1]['unum']:
                self.country_buff[country_buff_items[0][0]]['revive_seconds'] = duplicate_config['retreat_less'].get(country_buff_items[0][1]['unum'],default_revive_seconds)
                self.country_buff[country_buff_items[1][0]]['revive_seconds'] = default_revive_seconds
            else:
                self.country_buff[country_buff_items[0][0]]['revive_seconds'] = default_revive_seconds
                self.country_buff[country_buff_items[1][0]]['revive_seconds'] = duplicate_config['retreat_less'].get(country_buff_items[1][1]['unum'],default_revive_seconds)
        else:
            self.country_buff[country_buff_items[0][0]]['revive_seconds'] = default_revive_seconds
            self.country_buff[country_buff_items[1][0]]['revive_seconds'] = default_revive_seconds
        self.status = 1

            

    #挪到manage中，本server不用了 
    def init_world_from_db(self):
        open_date = int(str(self.open_date).replace('-',''))
        #open_date = 20200426
        from database_backup import UserDuplicate_table as UD
        from sqlalchemy import and_,select

        #World
        #open_date = 20200526
        db_user = select([UD],and_(UD.c.open_date==open_date)).execute().fetchall()
        #db_user = select([UD],).execute().fetchall()
        random.seed(open_date)
        all_user_dict = {}
        group_level_dict = {}
        duplicate_config = game_config.duplicate
        group_index_dict = {}
        for item in game_config.system_simple['duplicate_groups']:
            group_index_dict[item['id']] = item['index']
        for item in db_user:
            item = dict(item)
            item['uid'] = int(item['uid'])
            all_user_dict[item['uid']] = item
            #group_level = '%s|%s' % (item['group_id'],item['level'])
            group_index = group_index_dict[item['group_id']]
            group_level = '%s|%s|%s' % (item['group_id'],item['level'],item['level']*10000+group_index)
            group_level_dict.setdefault(group_level, [])
            group_level_dict[group_level].append([item['uid'],item['power'],item['zone'],random.random()])
        least_num = duplicate_config['grouping']['least_number']
        court = duplicate_config['grouping']['court']
        dnum = 0
        refund_list = []
        status=None
        for k,v in sorted(group_level_dict.items(),key=lambda x:int(x[0].split('|')[2])):
            #人数不够退费
            ucount= len(v)              #此分段总人数
            if_refund = False
            if ucount < least_num:
                add_num = 1
                if_refund = True
            else:
                v.sort(key=lambda x:x[3])
                add_num = ucount/court     #此分段总场数
                if ucount%court != 0:
                    add_num += 1
            if dnum + add_num <= options.dnum:
                dnum += add_num
                continue
            self.group_id = k.split('|')[0]   #渠道
            self.level = int(k.split('|')[1])   #段位
            if if_refund:
                status = 8
                self.if_refund = True
                refund_list = v
                break
            ucount_one = ucount/add_num   #单场人数
            if ucount_one%2 != 0:
                ucount_one -=1
            server_dnum = options.dnum-dnum    #本server在此段第几场
            u_list = v[server_dnum*ucount_one:(server_dnum+1)*ucount_one]
            if ucount_one * add_num != ucount%add_num:
                m = 0
                n = 0
                for item in v[add_num*ucount_one:]:
                    if m%add_num==server_dnum:
                        u_list.append(item)
                    n += 1
                    if n%2 ==0:
                        m += 1
            u_list.sort(key=lambda x:x[1],reverse=True)
            country_list = duplicate_config['grouping']['country'][:2]
            self.country_buff = {
                    country_list[0][0]: {
                        'unum': 0,              
                        'buff': [0.0,0.0],              
                        'clear_monster': 0,                     #清除野怪数 
                        'capital': country_list[0][1],          #首都
                        'enemy': country_list[1][0],            #敌国
                        'enemy_capital': country_list[1][1],    #敌国首都
                        },
                    country_list[1][0]: {
                        'unum': 0,              
                        'buff': [0.0,0.0],              
                        'clear_monster': 0,                     #清除野怪数 
                        'capital': country_list[1][1],          #首都
                        'enemy': country_list[0][0],            #敌国
                        'enemy_capital': country_list[0][1],    #敌国首都
                        },
                    }
            if options.dnum%2 == 0:
                country_list = country_list[::-1]

            city_troop_ids = {}
            udid_list = []
            x = 0
            y = 1
            init_energy = duplicate_config['energy'][0]
            uname_dict = {}
            for item in u_list:
                country_index = x%2
                country, cid = country_list[country_index]
                uid = item[0]
                city_troop_ids.setdefault(cid,[])
                self.country_buff[country]['unum'] += 1
                u_dict = all_user_dict[uid]
                u_dict['country'] = country
                uname = u_dict['uname']
                uname_dict.setdefault(uname,0)
                uname_num = uname_dict[uname]
                if uname_num:
                    u_dict['uname'] = '%s%s' % (uname,uname_num)
                uname_dict[uname] += 1
                u_dict['hero_data'] = pickle.loads(str(u_dict['hero_data']))
                troop_data = pickle.loads(str(u_dict['troop_data']))

                for hid,h_data in troop_data.items():
                    h_data['uid'] = u_dict['uid']
                    h_data['country'] = u_dict['country']
                    h_data['uname'] = u_dict['uname']
                    h_data['head'] = u_dict['head']
                    h_data['city'] = cid
                    h_data['status'] = 0
                    h_data['rush'] = 0
                    h_data['data'] = None
                    h_data['energy'] = init_energy
                    h_data['energy_buy_times'] = 0
                    h_data['revive_times'] = 0

                    city_troop_ids[cid].append('%s_%s' % (uid, hid))
                self.troops[uid] = troop_data

                user = User.loads(u_dict)
                User.users[user.uid] = user
                udid_list.append(u_dict['udid'])
                


                y += 1
                if y%2==0:
                    x += 1
            UD.update(and_(UD.c.udid.in_(udid_list))).execute(dnum=options.dnum)
            num_add = duplicate_config['monster']['num_add']


            for x in range(9):
                for y in range(9):
                    cid = str(x*1000+y)
                    city = City()
                    city.cid = cid
                    city.floor = duplicate_config['terrain'][y][x]
                    #city.country = duplicate_config['floor'][city.floor]['country']
                    city.country = duplicate_config['ascription_ini'][y][x]
                    #if duplicate_config['win'].has_key(city.country):
                    if self.country_buff.has_key(city.country):
                        #self.country_buff.setdefault(city.country,{'clear_monster':0,'buff':[0.0,0.0]})
                        dmg_all = duplicate_config['floor'][city.floor].get('dmg_all',0)
                        if dmg_all:
                            dmg_all = Decimal(str(dmg_all))
                            country_dmg_all = Decimal(str(self.country_buff[city.country]['buff'][0]))
                            country_dmg_all += dmg_all
                            self.country_buff[city.country]['buff'][0] = float(country_dmg_all)
                        res_all = duplicate_config['floor'][city.floor].get('res_all',0)
                        if res_all:
                            res_all = Decimal(str(res_all))
                            country_res_all = Decimal(str(self.country_buff[city.country]['buff'][1]))
                            country_res_all += res_all
                            self.country_buff[city.country]['buff'][1] = float(country_res_all)
                    if city_troop_ids.has_key(cid):
                        city.troop_ids = city_troop_ids[cid]
                    city.monster = duplicate_config['neutral'][y][x]
                    if city.monster != 0:
                        monster_country = duplicate_config['ascription'][y][x]
                        monster_num = duplicate_config['monster'][city.monster]['num']
                        #if country_unum.has_key(monster_country):
                        monster_num = int(math.ceil(monster_num*min(num_add[2],(num_add[0]+num_add[1]*len(u_list)))))
                        city.monster_num = monster_num
                    else:
                        city.monster_num = 0
                    self.cities[cid] = city

            country_buff_items = self.country_buff.items()
            default_revive_seconds = duplicate_config['retreat_passive'][0]
            if country_buff_items[0][1]['unum'] != country_buff_items[1][1]['unum']:
                if country_buff_items[0][1]['unum'] < country_buff_items[1][1]['unum']:
                    self.country_buff[country_buff_items[0][0]]['revive_seconds'] = duplicate_config['retreat_less'].get(country_buff_items[0][1]['unum'],default_revive_seconds)
                    self.country_buff[country_buff_items[1][0]]['revive_seconds'] = default_revive_seconds
                else:
                    self.country_buff[country_buff_items[0][0]]['revive_seconds'] = default_revive_seconds
                    self.country_buff[country_buff_items[1][0]]['revive_seconds'] = duplicate_config['retreat_less'].get(country_buff_items[1][1]['unum'],default_revive_seconds)
            else:
                self.country_buff[country_buff_items[0][0]]['revive_seconds'] = default_revive_seconds
                self.country_buff[country_buff_items[1][0]]['revive_seconds'] = default_revive_seconds
            status = 1
            break
        else:
            status =9 
        if refund_list:
            for item in v:
                User.push_refund(item[0],item[2])
        return status

    def change_troop_city(self, troop, city):
        troop_id = '%s_%s' % (troop['uid'], troop['hid'])
        from_city = self.cities[troop['city']]

        from_city.troop_ids.remove(troop_id)
        city.troop_ids.append(troop_id)
        troop['city'] = city.cid
        floor_conf = game_config.duplicate['floor'][city.floor]
        hp_add = floor_conf.get('hp_add',0)
        if hp_add:
            troop['army'][0]['hp'] = int(math.ceil(troop['army'][0]['hp']*(1+hp_add)))
            troop['army'][1]['hp'] = int(math.ceil(troop['army'][1]['hp']*(1+hp_add)))

        rush = floor_conf.get('rush',None)
        if rush == -1:
            troop['rush'] = 0

    def change_city_country(self, city, country):
        floor_conf = game_config.duplicate['floor'][city.floor]
        if floor_conf.get('river',None):
            return False
        old_country = city.country
        city.country = country
        dmg_all = floor_conf.get('dmg_all',0)
        if dmg_all:
            dmg_all = Decimal(str(dmg_all))
            if self.country_buff.has_key(old_country):
                old_country_dmg_all = Decimal(str(self.country_buff[old_country]['buff'][0]))
                old_country_dmg_all -= dmg_all
                self.country_buff[old_country]['buff'][0] = float(old_country_dmg_all)
            country_dmg_all = Decimal(str(self.country_buff[city.country]['buff'][0]))
            country_dmg_all += dmg_all
            self.country_buff[city.country]['buff'][0] = float(country_dmg_all)
        res_all = floor_conf.get('res_all',0)
        if res_all:
            res_all = Decimal(str(res_all))
            if self.country_buff.has_key(old_country):
                old_country_res_all = Decimal(str(self.country_buff[old_country]['buff'][1]))
                old_country_res_all -= res_all
                self.country_buff[old_country]['buff'][1] = float(old_country_res_all)
            country_res_all = Decimal(str(self.country_buff[city.country]['buff'][1]))
            country_res_all += res_all
            self.country_buff[city.country]['buff'][1] = float(country_res_all)
        city.occupy_time = [int(time.time()),0]
        city_dumps = city.get_small_city_dumps()

        self.push_all_user('dw.fight_end', {'city': city_dumps})

        #win_country_cid = game_config.duplicate['win'].get(country,None)
        win_country_cid = self.country_buff.get(country,{}).get('enemy_capital',None)
        u = User(-1)
        u.country = country
        u.check_chat('duplicate_fight_victory', [city.cid, city.country, city.floor])
        if city.cid == win_country_cid:
            self.status = 5
            self.win_type = 0  #攻占大本营
            self.win_country = country


    def push_all_user(self, method, push_data):
        d_score_num = 0
        advance_d_score = 0
        if method == 'dw.cross_service_finish':
            duplicate_config = game_config.duplicate
            d_score_num = game_config.duplicate['task']['winner']
            if self.win_type==0:
                x,y = game_config.duplicate['task']['advance']
                now = datetime.datetime.now()
                s = utils.total_seconds(self.duplicate_time[3]-now)
                advance_d_score = int(int(s/x) * y)
                d_score_num += advance_d_score
        for uid in User.users:
            u = User.users[uid]
            if d_score_num:
                if u.country == self.win_country:
                    u.add_d_score(d_score_num)
                    p = {'user': {'d_score': u.d_score}}
                    if advance_d_score:
                        u.task['advance_d_score'] = advance_d_score
                        p['user']['task'] = u.task
                    p.update(push_data)
                    u.write(method, p)
                    continue
            u.write(method, push_data)

    def set_country_leader(self):
        if_push = True
        for k, v in self.country_leader.items():
            if v['leader']:
                if_push = False
                break
            if v['uids']:
                uids = v['uids']
            else:
                uids = []
                for user in User.users.values():
                    if user.country == k:
                        uids.append(user.uid)
            u_list = []
            prestige_power = -1
            for uid in uids:
                user = User.users[uid]
                user_prestige_power = user.prestige+user.power/10000000.0
                if user_prestige_power > prestige_power:
                    prestige_power = user_prestige_power
                    v['leader'] = uid
        if if_push:
            self.push_all_user('dw.country_leader', self.country_leader)


    def join_country_leader(self, user, params):
        if user.uid not in self.country_leader[user.country]['uids']:
            self.country_leader[user.country]['uids'].append(user.uid)
            for item_user in User.users.values():
                if item_user.country != user.country:
                    continue
                item_user.write('dw.country_leader', self.country_leader)
        return True

    def quit_country_leader(self, user, params):
        if user.uid in self.country_leader[user.country]['uids']:
            self.country_leader[user.country]['uids'].remove(user.uid)
            for item_user in User.users.values():
                if item_user.country != user.country:
                    continue
                item_user.write('dw.country_leader', self.country_leader)
        return True

    def country_leader_set_city(self, user, params):
        cid = params['cid']
        if user.uid == self.country_leader[user.country]['leader']:
            if_push = False
            if cid not in self.country_leader[user.country]['cids']:
                if len(self.country_leader[user.country]['cids']) < game_config.duplicate['sign']:
                    self.country_leader[user.country]['cids'].append(cid)
                    if_push = True
            else:
                if_push = True
                self.country_leader[user.country]['cids'].remove(cid)
            if if_push:
                for item_user in User.users.values():
                    if item_user.country != user.country:
                        continue
                    item_user.write('dw.country_leader', self.country_leader)

        return True

    def get_info(self, user, params):
        troops = {}

        for uid,troop_data in self.troops.items():
            troops[uid] = {}
            for k,v in troop_data.items():
                troops[uid][k] = {
                        'uid': v['uid'],
                        'country': v['country'],
                        'hid': v['hid'],
                        'city': v['city'],
                        'status': v['status'],
                        'army': v['army'],
                        'power': v['power'],
                        'data': v['data'],
                        'energy': v['energy'],
                        'awaken': v.get('awaken',None),
                        'skin_id': v.get('skin_id',None),
                        'group': v.get('group',None),
                        'energy_buy_times': v['energy_buy_times'],
                        'revive_times': v['revive_times'],

                        }

        cities = {}
        for city in self.cities.values():
            #cities[city.cid] = city.dumps()
            cities[city.cid] = city.get_small_city_dumps()
        res = {}
        res['troops'] = troops
        res['cities'] = cities
        res['level'] = self.level
        res['status'] = self.status
        res['win_type'] = self.win_type
        res['win_country'] = self.win_country
        user.if_login = 1
        return res

    def get_duplicate_result(self,user,params):
        top_kill = [None,0]
        top_occupy = [None,0]
        user_task = {}
        for user in User.users.values():
            kill_num = user.task.get('kill',{}).get('user',0)
            user_task[user.uid] = user.task
            if kill_num > top_kill[1]:
                top_kill = [user.uid, kill_num]
            occupy_num = user.task.get('plough',[0,0])[0]
            if occupy_num > top_occupy[1]:
                top_occupy = [user.uid, occupy_num]
        result_dict ={
                'win_type': self.win_type,
                'win_country': self.win_country,
                'top_kill': top_kill,
                'top_occupy': top_occupy,
                'user_task': user_task,
                }
        return result_dict


    def get_troop_move_city_list(self, user_country, troop_city_id, to_city_id):
        duplicate_config = game_config.duplicate

        to_x = int(to_city_id)/1000
        to_y = int(to_city_id)%1000

        troop_x = int(troop_city_id)/1000
        troop_y = int(troop_city_id)%1000

        city_ids = [troop_city_id]

        if user_country == duplicate_config['ascription'][troop_y][troop_x] == user_country:
            if troop_x != to_x:
                for item in range(100):
                    if troop_x > to_x:
                        troop_x -= 1
                    else:
                        troop_x += 1
                    city_ids.append(str(troop_x*1000+troop_y))
                    if troop_x == to_x:
                        break
            if troop_y != to_y:
                for item in range(100):
                    if troop_y > to_y:
                        troop_y -= 1
                    else:
                        troop_y += 1
                    city_ids.append(str(troop_x*1000+troop_y))
                    if troop_y == to_y:
                        break
        else:
            if troop_y != to_y:
                for item in range(100):
                    if troop_y > to_y:
                        troop_y -= 1
                    else:
                        troop_y += 1
                    city_ids.append(str(troop_x*1000+troop_y))
                    if troop_y == to_y:
                        break
            if troop_x != to_x:
                for item in range(100):
                    if troop_x > to_x:
                        troop_x -= 1
                    else:
                        troop_x += 1
                    city_ids.append(str(troop_x*1000+troop_y))
                    if troop_x == to_x:
                        break


        duplicate_config = game_config.duplicate
        speed = duplicate_config['speed']['move_speed']

        city_list = []
        for cid in city_ids:
            floor = self.cities[cid].floor
            rush = duplicate_config['floor'][floor].get('rush',None)
            if rush == 1:
                speed = duplicate_config['speed']['rush_speed']
            elif rush == -1:
                speed = duplicate_config['speed']['move_speed']
            speed_rate = duplicate_config['floor'][floor].get('speed_rate',None)
            if speed_rate:
                city_list.append([cid, int(speed*speed_rate)])
            else:
                city_list.append([cid, speed])


        return city_list



    def troop_move(self, user, params_list):
        if self.status != 3:
            raise Model_Error(10478, '战斗未开始')
        now = int(time.time())
        duplicate_config = game_config.duplicate
        go_ini = 0
        for params in params_list:
            hid = params['hid']

            troop = self.troops[user.uid][hid]

            to_city_id = str(params['to_city_id'])
            if troop['city'] == to_city_id:
                raise Model_Error(10448, "出发地是目的地")
            to_city_floor = self.cities[to_city_id].floor
            if duplicate_config['floor'][to_city_floor].get('river', 0):
                raise Model_Error(10602, "目的地不能驻军")

            city_list = self.get_troop_move_city_list(user.country, troop['city'], to_city_id)
            move_energy = duplicate_config['energy'][2]
            if len(city_list)-1+move_energy > troop['energy']:
                raise Model_Error(10601, "体力不足")

            if not(hid in self.troops[user.uid]):
                raise Model_Error(10436, "不存在的英雄id")


            if troop['status']!=0 or self.cities[troop['city']].fight:
                raise Model_Error(10446, "英雄部队非待命状态")

            user_speedup = 1
            
            start_time = now+go_ini*duplicate_config['go_ini']
            troop['data'] = {
                    'city_list': city_list,
                    'have_dis': 0,
                    'start_time': start_time,
                    'speedup': 1,
                    'index': 0,
                    'last_time': start_time,
                    'type': 0
                    }
            floor = self.cities[troop['city']].floor
            rush = duplicate_config['floor'][floor].get('rush', 0)
            if rush == 1:
                troop['rush'] = 1
            else:
                troop['rush'] = 0



            troop['status'] = 1
            troop['energy'] -= move_energy
            user.move_times += 1
            self.push_all_user('dw.troop_move_push', self.troops[user.uid][hid])
            go_ini += 1
            world.user_move_times += 1
            
        return True

    def troop_runaway(self, user, params, lose_runaway=False):
        if self.status != 3 and not lose_runaway:
            raise Model_Error(10478, '战斗未开始')
        now = int(time.time())
        hid = params['hid']


        if not(hid in self.troops[user.uid]):
            raise Model_Error(10431, "不存在的英雄id")
        troop = self.troops[user.uid][hid]
        city = self.cities[troop['city']]


        if city.fight and not lose_runaway:
            raise Model_Error(10603, '战斗中不能撤军')

        if troop['status'] != 0:
            raise Model_Error(10604, '不满足撤军状态')
        duplicate_config = game_config.duplicate
        for item in duplicate_config['grouping']['country']:
            if item[0] == user.country:
                to_city_id = item[1]
                break
        troop_city_id = troop['city']
        if to_city_id == troop['city'] and not lose_runaway:
            raise Model_Error(10604, '不满足撤军状态')

        to_x = int(to_city_id)/1000
        to_y = int(to_city_id)%1000

        troop_x = int(troop_city_id)/1000
        troop_y = int(troop_city_id)%1000


        if troop_x == to_x:
            dis = abs(troop_y-to_y)*duplicate_config['speed']['floor_distance']
        elif troop_y == to_y:
            dis = abs(troop_x-to_x)*duplicate_config['speed']['floor_distance']
        else:
            x = abs(troop_x-to_x)
            y = abs(troop_y-to_y)
            z = pow(x**2+y**2,0.5)
            dis = z*duplicate_config['speed']['floor_distance']
        if lose_runaway:
            speed = duplicate_config['speed']['flee_speed']
        else:
            now_d = datetime.datetime.now()
            pass_seconds = utils.total_seconds(now_d-self.duplicate_time[2])
            special_speed,timeScale = self.get_special(pass_seconds)
            speed = duplicate_config['speed']['retreat_speed']*special_speed
        need_seconds = int(dis/speed)
        end_time = now+need_seconds

        troop['status'] = 2
        troop['data'] = {
                'start_time': now,
                'end_time': end_time,
                'to_city_id': to_city_id,
                }
        if lose_runaway:
            troop['data']['type'] = 3
            troop['army'][0]['hp'] = 0
            troop['army'][1]['hp'] = 0
        else:
            troop['data']['type'] = 2

        self.push_all_user('dw.troop_runaway_push', self.troops[user.uid][hid])


        return True

    def troop_move_dismiss(self, user, params):
        if self.status != 3:
            raise Model_Error(10478, '战斗未开始')
        now = int(time.time())
        hid = params['hid']


        if not(hid in self.troops[user.uid]):
            raise Model_Error(10431, "不存在的英雄id")
        troop = self.troops[user.uid][hid]
        #city = self.cities[troop['city']]

        if troop['status'] != 1 or troop.get('rush',0) == 1:
            raise Model_Error(10603, '不满足撤军状态')
        if troop['data']['index'] != 0:
            raise Model_Error(10603, '不满足撤军状态')


        have_dis = troop['data']['have_dis']
        city_list = troop['data']['city_list']
        dis_time = troop['data']['last_time'] - troop['data']['start_time']
        end_time = now + dis_time

        troop['status'] = 2
        troop['data'] = {
                'have_dis': have_dis,
                'city_list': city_list,
                'start_time': now,
                'end_time': end_time,
                'type': 1,
                'to_city_id': troop['city'],
                }

        self.push_all_user('dw.troop_move_dismiss_push', self.troops[user.uid][hid])


        return troop

    def get_special(self, pass_seconds):
        for item in game_config.duplicate['special_time']:
            special_speed = item[1]
            timeScale = item[2]
            if pass_seconds < item[0]*60:
                break
        return special_speed, timeScale
    #补兵，补体力，战鼓，加速，重伤，结算,战报，任务，聊天,成就
    @gen.coroutine
    def run(self, now_d):
        if self.status > 4 or self.status < 3:
            raise gen.Return(False)
        now = int(time.mktime(now_d.timetuple()))
        duplicate_config = game_config.duplicate

        #补充体力
        energy_add = duplicate_config['energy_add']
        pass_seconds = utils.total_seconds(now_d-self.duplicate_time[2])
        energy_add_num = 0
        energy_num = pass_seconds/energy_add[0]
        if energy_num > self.energy_num:
            energy_max = duplicate_config['energy'][1]
            energy_add_num = energy_add[1]*(energy_num-self.energy_num)
            self.energy_num = energy_num


        special_speed,timeScale = self.get_special(pass_seconds)
        #判断行军
        has_run = False
        for xuid in self.troops.keys():
            for xhid in self.troops[xuid].keys():
                troop = self.troops[xuid][xhid]
                #补充体力
                if energy_add_num:
                    if troop['energy'] < energy_max:
                        troop['energy'] += energy_add_num
                        if troop['energy'] > energy_max:
                            troop['energy'] = energy_max
                #troop.status:|0驻守|1行军|2撤回or撤军or败逃|4重伤
                #troop.data.type:|1撤回|2撤军|3败逃
                if troop['status']==4:
                    if now >= troop['data']['end_time']:
                        troop['status'] = 0
                        troop['data'] = None
                        troop['army'][0]['hp'] = max(troop['army'][0]['hp'],int(troop['army'][0]['hpm']*duplicate_config['retreat_passive'][1]))
                        troop['army'][1]['hp'] = max(troop['army'][1]['hp'],int(troop['army'][1]['hpm']*duplicate_config['retreat_passive'][1]))
                        self.push_all_user('dw.troop_move_delete', troop)
                        yield self.check_fire(troop, now) 
                elif troop['status']==2:
                    if now >= troop['data']['end_time']:
                        city = self.cities[troop['data']['to_city_id']]
                        self.change_troop_city(troop, city)
                        if troop['data']['type'] ==3:
                            troop['status'] = 4
                            troop['data']['start_time'] = now
                            #troop['data']['end_time'] = now+duplicate_config['retreat_passive'][0]
                            troop['data']['end_time'] = now+self.country_buff[troop['country']]['revive_seconds']
                            self.push_all_user('dw.troop_move_delete', troop)
                        else:
                            if troop['data']['type'] ==2:
                                retreat_active_hp_0 = int(math.ceil(troop['army'][0]['hpm']*duplicate_config['retreat_active']))
                                retreat_active_hp_1 = int(math.ceil(troop['army'][1]['hpm']*duplicate_config['retreat_active']))
                                troop['army'][0]['hp'] = max(troop['army'][0]['hp'],retreat_active_hp_0)
                                troop['army'][1]['hp'] = max(troop['army'][1]['hp'],retreat_active_hp_1)
                            troop['status'] = 0
                            troop['data'] = None
                            self.push_all_user('dw.troop_move_delete', troop)

                            yield self.check_fire(troop, now) 


                elif troop['status']==1:
                    has_run = True
                    if troop['data'] is None:
                        troop['status'] = 0
                        app_log.error('data is None')
                        continue

                    if troop['data'].get('index', 0)==len(troop['data']['city_list'])-1:
                        app_log.error('index have hit last city.')
                        troop['status'] = 0
                        troop['data'] = None
                        continue
                    if now<=troop['data']['start_time']:
                        continue

                    if now==troop['data'].get('last_time', troop['data']['start_time']):
                        #app_log.error('time not pass??')
                        continue
                    
                    index = troop['data'].get('index', 0)
                    _, speed = troop['data']['city_list'][index]


                    pass_dis = int(special_speed*speed*(now-troop['data'].get('last_time', troop['data']['start_time'])))
                    have_dis = troop['data']['have_dis']+pass_dis
                    dis = duplicate_config['speed']['floor_distance']
                    is_finish = False 
                    is_fire = False 
                    while 1:
                        cid, _ = troop['data']['city_list'][index]
                        #dis = self.get_two_cities_dis(troop['country'], cid, troop['data']['city_list'][index+1][0])
                        if have_dis>=dis:
                            index += 1
                            cid, _ = troop['data']['city_list'][index]
                            city = self.cities[str(cid)]
                            self.change_troop_city(troop, city)
                            troop['data']['index'] = index
                            troop['energy'] -= duplicate_config['energy'][3]
                            if index==len(troop['data']['city_list'])-1:
                                is_finish = True
                                troop['data'] = None
                            if city.fight:
                                is_fire = True
                                break
                            if city.country!=troop['country']:
                                if city.monster_num >0:
                                    is_fire = True
                                    break
                                if len(city.troop_ids)>1:
                                    if not duplicate_config['floor'][city.floor].get('river',None):
                                        for city_troop_id in city.troop_ids:
                                            city_troop_uid,city_troop_hid = city_troop_id.split('_')
                                            city_troop = self.troops[int(city_troop_uid)][city_troop_hid]
                                            if city_troop['status'] == 0:
                                                if city_troop['country'] != troop['country']:
                                                    is_fire = True
                                                    break
                                    if is_fire:
                                        break
                                if duplicate_config['floor'][city.floor].get('wait',0) > 0:
                                    is_fire= True
                                    break

                                self.change_city_country(city, troop['country'])
                                troop_u = User.users[troop['uid']]
                                troop_u.check_task('occupy', {'floor': city.floor})
                                troop_u.effort_records['floor_dict'].setdefault(city.floor,0)
                                troop_u.effort_records['floor_dict'][city.floor] += 1
                            if is_finish:
                                break




                            if duplicate_config['floor'][city.floor].get('rush',0) == 1:
                                troop['rush'] = 1
                            have_dis -= dis
                            continue
                        else:
                            break
                    if is_finish or is_fire:
                        troop['status'] = 0
                        #到达终点, 途经点火城市，途经有驻军的敌城
                        self.push_all_user('dw.troop_move_delete', troop)
                        if is_fire:
                            yield self.check_fire(troop, now)
                        if not troop.get('rush',0):
                            troop['data'] = None

                    else:
                        troop['data']['last_time'] = now
                        troop['data']['have_dis'] = have_dis
        raise gen.Return(has_run)
    @gen.coroutine
    def speed_up_fight(self, user, params):
        city = self.cities[str(params['cid'])]
        if not(city.fight):
            raise Model_Error(10477, '没有战斗')
        if not(city.fight.get('last_battle_result')):
            raise Model_Error(10478, '战斗未开始')
        have_me = False

        duplicate_config = game_config.duplicate
        for i in [0,1]:
            troop_list = city.fight['team'][i]['troop']+[city.fight['last_battle_result']['initJS']['troop'][i]]
            for troop in troop_list:
                if user.uid==troop['uid']:
                    have_me = True
                    if len(troop_list) < duplicate_config['floor'][city.floor]['speedUp']:
                        raise Model_Error(10482, '战鼓需要的部队数不足')
        if not(have_me):
            raise Model_Error(10479, '自己不在战斗中')
        if city.fight.get('speedUp', 0) != 0:
            raise Model_Error(10480, '已经在使用加速')
        need_coin = duplicate_config['speedUp'][0]


        user_coin = yield user.spend_coin(need_coin, 'duplicate_speed_up_fight')
        if user_coin is None:
            raise Model_Error(18888, self.get_return_msg('lack_of_coin'))

        city.fight['speedUp'] = game_config.world['countryFightSpeedUpNum']
        city.fight['fight_time'] = time.time()

        city.push_follow_fight('dw.speed_up_fight_follow', {'city': city.cid, 'speedUp': city.fight['speedUp'], 'uid': user.uid, 'uname': user.uname, 'country': user.country})
        user.add_d_score(duplicate_config['speedUp'][1])
        user.task.setdefault('speed_up_times',0) 
        user.task['speed_up_times'] += 1


        return_dict = {
                'user': {'task': user.task,'coin':user_coin, 'd_score': user.d_score},
                'troop': troop,
                }

        raise gen.Return(return_dict)

    def city_fire(self, now, fireCountry, city, attack_army, defend_army):
        fire_ready_time = game_config.duplicate['floor'][city.floor]['wait']
        country_logs = {}

        if self.country_buff.has_key(fireCountry) and self.country_buff[fireCountry]['enemy_capital'] == city.cid:
            clear_monster = self.country_buff[fireCountry]['clear_monster']
            if clear_monster > 0:
                old_dmg_all, old_res_all = self.country_buff[fireCountry]['buff']

                old_dmg_all = Decimal(str(old_dmg_all))
                old_res_all = Decimal(str(old_res_all))
                clear_monster = Decimal(str(clear_monster))
                add_dmg_all, add_res_all = game_config.duplicate['atk_win_buff']
                dmg_all = Decimal(str(add_dmg_all))*clear_monster+old_dmg_all
                res_all = Decimal(str(add_res_all))*clear_monster+old_res_all
                buff_list = [float(dmg_all), float(res_all)]
                enemy = self.country_buff[fireCountry]['enemy']
                country_logs = {
                        fireCountry: buff_list,
                        enemy: self.country_buff[enemy]['buff'],
                        }
            else:
                country_logs = self.country_buff
        else:
            country_logs = self.country_buff





        city.fight = {
                'city': city.cid,
                'fireTime': now, 
                'fight_time': now+fire_ready_time,
                'fireCountry': fireCountry, 
                'rnd': random.randint(1, 65535), 
                'team': [
                    {'halo': [], 'record': {}, 'dead': {}, 'troop': attack_army}, 
                    {'halo': [], 'record': {}, 'dead': {}, 'troop': defend_army}, 
                ],
                'user_logs': {},
                'country_logs': country_logs,
                'fight_count': 0, 
                'follow_uids': [], 
                }

        self.push_all_user('dw.city_fire', {'city': city.cid, 'fire_country': fireCountry})

        for i, item in enumerate(defend_army):
            item['proud'] = 0
            if item['uid']>0:
                User.users[item['uid']].write('dw.join_fight', {'index': i,'city': city.cid, 'hid': item['hid']})
            self.add_fight_troop_log(city, item)

        for i, item in enumerate(attack_army):
            item['proud'] = 0
            if item['uid']>0:
                User.users[item['uid']].write('dw.join_fight', {'index': i,'city': city.cid, 'hid': item['hid']})
            self.add_fight_troop_log(city, item)

    @gen.coroutine
    def check_fire(self, troop, now):
        troop = copy(troop)
        if troop.has_key('data'):
            del troop['data']
        cid = troop['city']
        city = self.cities[cid]
        troop['proud'] = 0

        

        if city.fight:
            #加入战斗
            side = 0
            if city.country==troop['country']:
                team = city.fight['team'][1]['troop']
                side = 1
            else:
                team = city.fight['team'][0]['troop']
                side = 0
            






            x = 0
            for i in range(len(team)-1, -1, -1):
                if team[i]['uid']>0:
                    x = i+1
                    break
            index = max(x, min(len(team), game_config.fight['troopInsertIndex']))
            team.insert(index,troop)





            if len(team)==1:
                front = None
            else:
                front = [team[-2]['uid'], team[-2]['hid']]
            self.add_fight_troop_log(city, troop)
            
            if troop['uid']>0:
                User.users[troop['uid']].write('dw.join_fight', {'index': index,'city': city.cid, 'hid': troop['hid']})

            if city.country!=troop['country'] and city.fight['fireCountry']!=troop['country']:
                for uid in User.users:
                    if User.users[uid].country==troop['country']:
                        User.users[uid].write('dw.join_fire', {'city': city.cid})

            city.push_follow_fight('dw.join_fight_follow', {'index': index,'city': city.cid, 'side': side, 'army': troop, 'user_log': city.fight['user_logs'].get(troop['uid']), 'country_log': city.fight['country_logs'].get(troop['country']), 'front': front})

        elif city.country!=troop['country']:
            #触发点火
            
            defend_army = city.get_defend_troops()



            #加入城防军
            if city.monster_num >0:
                duplicate_config = game_config.duplicate
                hlv = duplicate_config['monster']['lv'][self.level]
                hid = duplicate_config['monster'][city.monster]['hero']
                prepare_data = [{
                    'uid': -1,
                    'country': city.country,
                    'lv': hlv,
                    'hid': hid,
                    }]
                tt = yield RequestCollection.FightServerRequest('data',prepare_data)
                tt = tt[0]
                bot_army = []
                for i in range(city.monster_num):
                    bot_troop = copy(tt)
                    bot_troop['uid'] = -i-1
                    bot_army.append(bot_troop)

                defend_army += bot_army

            fireCountry = troop['country']
            attack_army = [troop]
            self.city_fire(now, fireCountry, city, attack_army, defend_army)

    def follow_fight(self, user, params):
        cid = str(params['cid'])
        city = self.cities[cid]
        if not(city.fight):
            raise Model_Error(10476, "该城市没有战斗")

        if user.uid not in city.fight['follow_uids']:
            city.fight['follow_uids'].append(user.uid)

        return city.fight

    def test_zip(self, user, params):
        return {'test': 'test_zip'}

    def unfollow_fight(self, user, params):
        cid = str(params['cid'])
        city = world.cities[cid]
        if city.fight and user.uid in  city.fight['follow_uids']:
            city.fight['follow_uids'].remove(user.uid)
        return True

    def get_troop_hp(self, troop):
        awaken = 0
        skin = 0
        if troop.get('awaken',None) == 1:
            awaken = 1
        if troop.get('skin_id',None):
            skin = troop['skin_id'].split('_')[1]
        return [troop['army'][0]['hpm']+troop['army'][1]['hpm'], troop['army'][0]['hp']+troop['army'][1]['hp'], troop['proud'], '%s_%s_%s' % (troop['hid'], awaken, skin)]

    def add_fight_troop_log(self, city, troop):
        uid = troop['uid']
        if uid > 0:
            if uid in city.fight['user_logs']:
                if troop['power']>city.fight['user_logs'][uid]['power']:
                    city.fight['user_logs'][uid]['power'] = troop['power']

                city.fight['user_logs'][uid]['attends'][troop['hid']] = self.get_troop_hp(troop)
                city.fight['user_logs'][uid]['hp'] += (troop['army'][0]['hp']+troop['army'][1]['hp'])
            else:
                city.fight['user_logs'][uid] = {
                        'uname': troop['uname'], 
                        'head': troop['head'], 
                        'country': troop['country'],
                        'power': troop['power'],
                        'kill': 0, 
                        'dead': 0, 
                        'attends': {
                            troop['hid']: self.get_troop_hp(troop)
                            }, 
                        'hp': (troop['army'][0]['hp']+troop['army'][1]['hp'])
                        }
        



    @gen.coroutine
    def run_fight(self,now_d):
        if self.status > 4 or self.status < 3:
            raise gen.Return(False)
        now = int(time.mktime(now_d.timetuple()))

        duplicate_config = game_config.duplicate
        #恢复血量
        recovery_time = duplicate_config['recovery_time']
        hp_add = 0
        pass_seconds = utils.total_seconds(now_d-self.duplicate_time[2])
        recovery_num = pass_seconds/recovery_time
        if recovery_num > self.recovery_num:
            hp_add = recovery_num - self.recovery_num
            self.recovery_num = recovery_num

        special_speed,timeScale = self.get_special(pass_seconds)
        #判断战斗
        hold_config = duplicate_config['task']['hold']
        #user_country_list = duplicate_config['win'].keys()
        user_country_list = self.country_buff.keys()
        has_fight = False
        for cid in self.cities:
            city = self.cities[cid]
            #恢复血量
            if hp_add:
                city_recovery = duplicate_config['floor'][city.floor].get('recovery',0)
                if city_recovery:
                    for troop_id in city.troop_ids:
                        troop_uid,troop_hid = troop_id.split('_')
                        city_troop = self.troops[int(troop_uid)][troop_hid]
                        if city_troop['status'] == 0 and not city.fight:
                            hp0 = city_troop['army'][0]['hp']
                            hpm0 = city_troop['army'][0]['hpm']
                            hp1 = city_troop['army'][1]['hp']
                            hpm1 = city_troop['army'][1]['hpm']
                            if hp0 < hpm0:
                                new_hp0 = hp0 + int(math.ceil(hpm0*city_recovery*hp_add))
                                city_troop['army'][0]['hp'] = min(new_hp0,hpm0)
                            if city_troop['army'][1]['hp'] < hpm1:
                                new_hp1 = hp1 + int(math.ceil(hpm1*city_recovery*hp_add))
                                city_troop['army'][1]['hp'] = min(new_hp1,hpm1)
            #占领城市cd增加d_score
            if hold_config.has_key(cid):
                if city.country in user_country_list:
                    add_times = (now-city.occupy_time[0])/hold_config[cid][0]
                    add_num = add_times-city.occupy_time[1]
                    city.occupy_time[1] = add_times
                    if add_num >0:
                        for u in User.users.values():
                            if u.country == city.country:
                                u.check_task('hold', {'d_score_num': hold_config[cid][1],'cid': cid})


            if city.fight:
                has_fight = True
                if now>=city.fight['fight_time']:
                    #进攻
                    t1 = city.fight['team'][0]['troop']
                    #防守
                    t2 = city.fight['team'][1]['troop']

                    xx = city.fight.get('last_battle_result') 
                    if xx:
                        troop1, troop2 = xx['initJS']['troop']
                        fight_result = xx['result']

                        try:
                            yield self.get_battle_result(city, t1, t2, troop1, troop2, fight_result, now, now_d)
                        except:
                            app_log.error('get_battle_result Error', exc_info=True)
                            for uid in (troop1['uid'], troop2['uid']):
                                if uid>0:
                                    user = User.users[uid]
                                    user.write('push_msg', {'code': 1, 'msg': 'battle error, cid: %s' % (city.cid, )})

                        if city.fight and city.fight.has_key('last_battle_result'):
                            del city.fight['last_battle_result']
                        else:
                            continue

                    if (yield self.check_fight_result(city, t1, t2, now, now_d)):
                        continue

                    #start fight==========================
                    troop1 = t1[0]
                    troop2 = t2[0]
                    #-------------------------------------
                    #country_log = city.fight['country_logs'][troop1['country']]
                    if troop1['uid'] > 0:
                        if self.country_buff.has_key(city.country) and self.country_buff[city.country]['capital'] == city.cid:
                            clear_monster = self.country_buff[troop1['country']]['clear_monster']
                            if clear_monster > 0:
                                old_dmg_all, old_res_all = self.country_buff[troop1['country']]['buff']
                                old_dmg_all = Decimal(str(old_dmg_all))
                                old_res_all = Decimal(str(old_res_all))
                                clear_monster = Decimal(str(clear_monster))

                                add_dmg_all, add_res_all = duplicate_config['atk_win_buff']
                                dmg_all = Decimal(str(add_dmg_all))*clear_monster+old_dmg_all
                                res_all = Decimal(str(add_res_all))*clear_monster+old_res_all
                                buff_list = [float(dmg_all), float(res_all)]
                            else:
                                buff_list = self.country_buff[troop1['country']]['buff'][:]
                        else:
                            buff_list = self.country_buff[troop1['country']]['buff']
                        troop1['others'] = {
                                'attends': city.fight['user_logs'][troop1['uid']]['attends'],
                                'buff': buff_list
                                }
                        spirit = []
                        for troop in t1:
                            if troop['uid']==troop1['uid'] and troop['hid'] in game_config.fight['legendTalentFight']:
                                spirit.append([troop['hid'], troop.get('hero_star', 0)])
                        if spirit:
                            troop1['others']['spirit'] = spirit

                    #-------------------------------------
                    #country_log = city.fight['country_logs'][troop2['country']]
                    if troop2['uid'] > 0:
                        troop2['others'] = {
                                'attends': city.fight['user_logs'][troop2['uid']]['attends'],
                                'buff': self.country_buff[troop2['country']]['buff']
                                }
                        spirit = []
                        for troop in t2:
                            if troop['uid']==troop2['uid'] and troop['hid'] in game_config.fight['legendTalentFight']:
                                spirit.append([troop['hid'], troop.get('hero_star', 0)])
                        if spirit:
                            troop2['others']['spirit'] = spirit

                    initJS = {'duplicate_level': self.level,'timeScale': timeScale,'mode': 80,'cid': int(cid), 'rnd': city.fight['rnd'], 'speedUp': city.fight.get('speedUp', 0), 'troop': [troop1, troop2], 'fight_count': city.fight['fight_count']}


                    fight_result = yield RequestCollection.FightServerRequest('doFight', initJS)


                    if city.fight.get('speedUp', 0)!=0:
                        city.fight['speedUp'] -= 1

                    t1.pop(0)
                    t2.pop(0)
                    city.fight['fight_time'] += fight_result['time']
                    city.fight['last_battle_result'] = {'city': city.cid, 'result': fight_result, 'initJS': initJS}

                    # 国战一场结束 

                    city.push_follow_fight('dw.finish_fight_follow', {'city': city.cid, 'result': fight_result, 'initJS': initJS})

                    #end fight==========================
        raise gen.Return(has_fight)

    @gen.coroutine 
    def get_battle_result(self, city, t1, t2, troop1, troop2, fight_result, now, now_d):

        #记录战报
        self.add_fight_log(city.cid, fight_result['rnd'], fight_result['winner'], troop1, troop2, now_d)


        #计算战功
        #杀人战功=击杀部队数*双方战力倍数*旗子*爵位加成*玩家或者npc系数*杀人系数
        #牺牲战功=牺牲部队数量*部队等级系数*爵位*旗子*牺牲系数

        city.fight['fight_count'] += 1
        city.fight['rnd'] = fight_result['rnd']


        if fight_result['winner']==0:
            win_troop = troop1
            dead_troop = troop2
            win_team = t1
        else:
            win_troop = troop2
            dead_troop = troop1
            win_team = t2




        winer_kill_hp = dead_troop['army'][0]['hp']+dead_troop['army'][1]['hp']
        loser_kill_hp = max(0, win_troop['army'][0]['hp']-fight_result['winnerHp'][0])+max(0, win_troop['army'][1]['hp']-fight_result['winnerHp'][1])

        win_troop['army'][0]['hp'] = fight_result['winnerHp'][0]
        win_troop['army'][1]['hp'] = fight_result['winnerHp'][1]
        if fight_result.get('winnerPuppetHp', None) is not None:
            win_troop['puppet']['hp'] = fight_result['winnerPuppetHp']
        win_troop['proud'] -= 1
        win_troop['rush'] = 0
        dead_troop['army'][0]['hp'] = 0
        dead_troop['army'][1]['hp'] = 0


        #战斗胜利或失败推送
        if win_troop['uid']>0:
            city.fight['user_logs'][win_troop['uid']]['kill'] += winer_kill_hp
            city.fight['user_logs'][win_troop['uid']]['dead'] += loser_kill_hp

            city.fight['user_logs'][win_troop['uid']]['attends'][win_troop['hid']] = self.get_troop_hp(win_troop)



            

            #index = len(win_team)

            x = 0
            for i in range(len(win_team)-1, -1, -1):
                if win_team[i]['uid']>0:
                    x = i+1
                    break
            index = max(x, min(len(win_team), game_config.fight['troopInsertIndex']))


            self.troops[win_troop['uid']][win_troop['hid']]['army'][0]['hp'] = win_troop['army'][0]['hp']
            self.troops[win_troop['uid']][win_troop['hid']]['army'][1]['hp'] = win_troop['army'][1]['hp']
            self.troops[win_troop['uid']][win_troop['hid']]['rush'] = 0
            try:
                army = self.troops[win_troop['uid']][win_troop['hid']]['army'] 
            except:
                army = None
            u = User.users[win_troop['uid']]
            u.write('dw.finish_fight', {'city': city.cid, 'hid': win_troop['hid'], 'index': index, 'army': army, 'user':{} })
            if dead_troop['uid'] > 0:
                u.check_task('kill_troop',{'floor': 'user'})
                u.effort_records['kill_user'] += 1
            else:
                u.check_task('kill_troop',{'floor': city.monster})
                u.effort_records['kill_monster'] += 1
        else:
            index = 0

        win_team.insert(index, win_troop)

        if dead_troop['uid']>0:
            city.fight['user_logs'][dead_troop['uid']]['kill'] += loser_kill_hp
            city.fight['user_logs'][dead_troop['uid']]['dead'] += winer_kill_hp

            city.fight['user_logs'][dead_troop['uid']]['attends'][dead_troop['hid']] = self.get_troop_hp(dead_troop)

            army = self.troops[dead_troop['uid']][dead_troop['hid']]['army'] 
            army[0]['hp'] = 0
            army[0]['hp'] = 0

            u = User.users[dead_troop['uid']]
            u.check_task('kill_troop',{'floor': 'dead'})
            u.write('dw.finish_fight', {'city': city.cid, 'hid': dead_troop['hid'], 'index': -1, 'army': army, 
                        'user': {}})
            if dead_troop['uid'] in self.troops and self.troops[dead_troop['uid']].has_key(dead_troop['hid']):
                #败逃
                self.troop_runaway(User.users[dead_troop['uid']], {'hid': dead_troop['hid']}, lose_runaway=True)
        else:
            city.monster_num -= 1
            if city.monster_num == 0:
                if city.country == self.country_buff[win_troop['country']]['enemy']:
                    if self.country_buff[city.country]['capital'] != city.cid:
                        self.country_buff[win_troop['country']]['clear_monster'] += 1
                        duplicate_config = game_config.duplicate
                        if self.country_buff[win_troop['country']]['clear_monster'] == duplicate_config['monster_clear_num']:
                            dmg_all,res_all = duplicate_config['monster_clear_buff']
                            self.country_buff[win_troop['country']]['buff'][0] += dmg_all
                            self.country_buff[win_troop['country']]['buff'][1] += res_all

            self.push_all_user('dw.city_monster', {'cid': city.cid,'monster_num': city.monster_num})



        if not (yield self.check_fight_result(city, t1, t2, now, now_d)):
            # 我的部队即将上场
            for i, troop in enumerate(t1):
                if i+1==3 or ((i+1)%10==0 and i!=0):
                    if troop['uid']>0:
                        u = User.users[troop['uid']]
                        u.write('dw.fight_ready', {'city': city.cid, 'hid': troop['hid'], 'index': i})
            for i, troop in enumerate(t2):
                if i+1==3 or ((i+1)%10==0 and i!=0):
                    if troop['uid']>0:
                        u = User.users[troop['uid']]
                        u.write('dw.fight_ready', {'city': city.cid, 'hid': troop['hid'], 'index': i})


    @gen.coroutine
    def check_fight_result(self, city, t1, t2, now, now_d):
        if t1 and t2:
            raise gen.Return(False)
        
        city_fight = city.fight
        old_city_country = city.country

        if not t1:
            #进攻失败
            #print '=============进攻失败'
            fireCountry = city.fight['fireCountry']
            cityCountry = city.country
            city.fight = None

            city_dumps = city.get_small_city_dumps()
            self.push_all_user('dw.fight_end', {'city': city_dumps})

            #防守成功


        elif not t2:
            #攻占成功
            duplicate_config = game_config.duplicate
            
            self.change_city_country(city, city_fight['fireCountry'])
            for uid in city.fight['user_logs']:
                u = User.users[uid]
                if u.country == city.country:
                    u.check_task('occupy', {'floor': city.floor})
            #print '=============进攻成功', city.cid, old_city_country, city.country

            #判断是否重新点火
            city.fight = None

            attack_army = []
            defend_army = []
            attack_countries = set()
            for item in t1:
                item['proud'] = 0
                if item['country']==city.country:
                    defend_army.append(item)
                else:
                    attack_army.append(item)
                    attack_countries.add(item['country'])

            attack_countries = list(attack_countries)
            if len(attack_countries)>=1:
                fireCountry = sorted(attack_countries)[0]
                fire_ready_time = duplicate_config['floor'][city.floor]['wait']
                self.city_fire(now, fireCountry, city, attack_army, defend_army)
            else:
                go_ini = 0
                for item in defend_army:
                    #带rush的继续行军
                    item_data = self.troops[item['uid']][item['hid']].get('data',{})
                    if item_data and item.get('rush',0) == 1:
                        start_time = now+go_ini*duplicate_config['go_ini']
                        self.troops[item['uid']][item['hid']]['status'] = 1
                        self.troops[item['uid']][item['hid']]['rush'] = 1
                        self.troops[item['uid']][item['hid']]['data']['start_time'] = start_time
                        self.troops[item['uid']][item['hid']]['data']['last_time'] = start_time
                        self.troops[item['uid']][item['hid']]['data']['type'] = 0
                        self.troops[item['uid']][item['hid']]['data']['have_diss'] = 0
                        index = self.troops[item['uid']][item['hid']]['data']['index']
                        self.troops[item['uid']][item['hid']]['data']['city_list'] = self.troops[item['uid']][item['hid']]['data']['city_list'][index:]
                        self.troops[item['uid']][item['hid']]['data']['index'] = 0
                        self.push_all_user('dw.troop_move_push', self.troops[item['uid']][item['hid']])
                        go_ini += 1
                    else:
                        self.troops[item['uid']][item['hid']]['rush'] = 0


        
        raise gen.Return(True)

    def get_city_info(self, user, params):
        cid = str(params['cid'])

        city = self.cities[cid]

        res = []
        for troop_id in city.troop_ids:
            uid, hid = troop_id.split('_')
            uid = int(uid)
            troop = self.troops[uid][hid]
            if troop['status'] != 0:
                continue
            item = {
                    'uid': uid,
                    'hid': hid,
                    'uname': User.users[uid].uname,
                    'hpm': troop['army'][0]['hpm']+troop['army'][1]['hpm'],
                    'hp': troop['army'][0]['hp']+troop['army'][1]['hp'],
                    'skin_id': troop.get('skin_id',None),
                    'awaken': troop.get('awaken',None),
                    }
            res.append(item)


        city_res = city.get_small_city_dumps()
        city_res['troop_list'] = res
        return city_res

    def add_fight_log(self, cid, rnd, winner, troop1, troop2, now_d):
        if troop1['uid'] >0 and troop2['uid']>0:
            log_dict = {
                    'mode': 80,
                    'duplicate_level': self.level,
                    'fight_time': now_d,
                    'cid': cid,
                    'rnd': rnd,
                    'winner': winner,
                    'troop': [
                            {
                                'uid': troop1['uid'],
                                'hid': troop1['hid'],
                                'armyHP': [troop1['army'][0]['hp'],troop1['army'][1]['hp']],
                                'rush': troop1['rush'],
                                'proud': troop1['proud'],
                                'others': copy(troop1['others']),
                            },
                            {
                                'uid': troop2['uid'],
                                'hid': troop2['hid'],
                                'armyHP': [troop2['army'][0]['hp'],troop2['army'][1]['hp']],
                                'rush': troop2['rush'],
                                'proud': troop2['proud'],
                                'others': copy(troop2['others']),
                            },
                        ]
                    }
            self.log_index += 1
            fid = str(self.log_index)
            log_dict['fid'] = fid
            self.fight_logs[fid] = log_dict

            User.users[troop1['uid']].fight_log_ids.insert(0,fid)
            User.users[troop2['uid']].fight_log_ids.insert(0,fid)

    def get_fight_logs(self, user, params):
        uid = params.get('uid',None)
        log_list = []
        if uid:
            for fid in User.users[uid].fight_log_ids:
                log_list.append(self.fight_logs[fid])
        else:
            log_index = self.log_index
            for i in range(game_config.duplicate['report_all']):
                if log_index <=0:
                    break
                log_list.append(self.fight_logs[str(log_index)])
                log_index-=1
        return log_list

    def fight_review(self, user, params):
        return self.get_fight_initJS(params['fid'])

    def get_fight_initJS(self, fid):
        log_dict = self.fight_logs[fid]

        troops = []
        for log_troop in log_dict['troop']:
            troop = copy(self.troops[log_troop['uid']][log_troop['hid']])
            troop['army'][0]['hp'] = log_troop['armyHP'][0]
            troop['army'][1]['hp'] = log_troop['armyHP'][1]
            troop['rush'] = log_troop['rush']
            troop['proud'] = log_troop['proud']
            troop['others'] = log_troop['others']
            troops.append(troop)

        initJS = {'mode': 80, 'duplicate_level': log_dict['duplicate_level'],'winner': log_dict['winner'],'fight_time': log_dict['fight_time'],'cid': log_dict['cid'], 'rnd': log_dict['rnd'], 'troop': troops}
        return initJS









class User(Model):
    seq_attrs = ['uid','pf','country','head','zone','uname','pay_money','admin_pay','power','group_id','hero_data','is_online','online_log','sessionid','task','d_score','k_score','cost_coin','fight_log_ids','chat_records','banned_users','freeze_list','effort_records','move_times', 'prestige','prestige_add','like_uids','unlike_uids', 'if_login']
    users = {}

    chat_cache = []
    chat_log = []
    black_msg_re = {}

    def __init__(self, uid=None):
        now = datetime.datetime.now()
        self.uid = uid
        self.country = None
        self.head = None
        self.zone = None
        self.pf = None
        self.uname = None
        self.pay_money = None
        self.admin_pay = None
        self.power = None
        self.group_id = None
        self.hero_data = None

        self.is_online = False
        self.online_log = {'login_time': None, 'logout_time': None, 'on_time': 0, 'off_time': 0}
        self.sessionid = None
        self.task = {}
        self.d_score = 0
        self.k_score = 0
        self.cost_coin = 0
        self.move_times = 0
        self.prestige = 0
        self.prestige_add = 0
        self.fight_log_ids = []
        self.chat_records = {
                    '3': {'time': None, 'num': 0},
                    '4': {'time': None, 'num': 0},
                    }
        self.banned_users = {}
        self.freeze_list = [0,now,'','','']
        self.effort_records = {
                'kill_monster': 0,
                'kill_user': 0,
                'floor_dict': {},
                }
        self.like_uids = []
        self.unlike_uids = []
        self.if_login = 0

    def close_me(self, send_close=True):
        app_log.info('close_me, %s', self.uid)
        if self.uid in AppSocket.user_socket_dict:
            AppSocket.user_socket_dict[self.uid].close_by_me = True
            AppSocket.user_socket_dict[self.uid].close()
        self.on_logout()

    def get_online_time(self):
        now = datetime.datetime.now()
        return int(self.online_log['on_time']+utils.total_seconds(now-self.online_log['login_time']))

    def on_login(self, sock):
        app_log.info('on_login, %s', self.uid)

        if self.uid in AppSocket.user_socket_dict:
            self.close_me()

        AppSocket.user_socket_dict[self.uid] = sock
        now = datetime.datetime.now()

        self.is_online = True
        self.online_log['login_time'] = now

        if self.online_log['logout_time']:
            self.online_log['off_time'] += utils.total_seconds(now-self.online_log['logout_time'])



    def on_logout(self):
        app_log.info('on_logout, %s', self.uid)

        self.is_online = False

        if self.uid in AppSocket.user_socket_dict:
            del AppSocket.user_socket_dict[self.uid]

        self.online_log['on_time'] = 1#self.get_online_time()
        self.online_log['logout_time'] = datetime.datetime.now()

    def write(self, method, data):
        socket = AppSocket.user_socket_dict.get(self.uid)
        if socket and self.country>=0:
            try:
                json_res = json.dumps({'method': method,'data': data}, default=json_default)
                socket.write_message(json_res)
                #r_len = len(json_res)
                #app_log.info('WriteMessage: %s--%s--%s', self.uid, method, r_len)
            except:
                pass

    @classmethod
    def get_new_uid(cls, uid, zone):
        zone_config = game_config.zone
        if not zone_config[zone][1][0]:
            uid = settings.UIDADD*int(zone)+int(uid)
        return int(uid)

    def get_return_msg(self, msg_id):
        server_raise_pf = game_config.system_simple['server_raise_pf'].get(self.pf,None)
        if server_raise_pf:
            return_msg_config = getattr(game_config, 'server_raise_msg_%s' % server_raise_pf)
        else:
            return_msg_config = game_config.server_raise_msg
        msg = return_msg_config.get(msg_id,None)
        if not msg:
            if server_raise_pf:
                return_msg_config = getattr(game_config, 'return_msg_%s' % server_raise_pf)
            else:
                return_msg_config = game_config.return_msg
            msg = return_msg_config[msg_id]
        return msg


    @classmethod
    @gen.coroutine
    def login(cls, params):
        uid = params.get('uid')
        sessionid = params.get('sessionid')
        if not uid or not sessionid:
            raise gen.Return(None)



        user_code = params.get('user_code')
        md5_str,tstr,pf = sessionid.split('|')
        if md5_str != md5('%s%s%s' % (uid,tstr,settings.PWD)).hexdigest():
            raise gen.Return(None)

        zone = params['zone']
        uid = cls.get_new_uid(uid,zone)
        try:
            uid = int(uid)
        except:
            raise gen.Return(None)
        if not User.users.has_key(uid):
            raise gen.Return(None)
        self = User.users[uid]
        #if not game_config.zone[zone][7]:
        #    if self.zone != zone:
        #        raise gen.Return(None)
        now = datetime.datetime.now()
        self.now = now
        u_dict = self.dumps()
        users = {}
        for uid,user in User.users.items():
            users[uid] = {
                    'uname': user.uname,
                    'head': user.head,
                    'country': user.country,
                    'prestige': user.prestige,
                    }

        raise gen.Return({'country_leader': world.country_leader,'me':u_dict,'users': users})

    def user_info(self, params):
        uid = params['uid']
        item_user = User.users[uid]

        u_dict = {
                'uid': item_user.uid,
                'd_score': item_user.d_score,
                'uname': item_user.uname,
                'head': item_user.head,
                'prestige': item_user.prestige,
                'power': item_user.power,
                'country': item_user.country,
                'hero_data': item_user.hero_data,
                }

        return u_dict



    @gen.coroutine
    def buy_energy(self, params):
        if world.status != 3:
            raise Model_Error(10478, '战斗未开始')
        hid = params['hid']
        troop = world.troops[self.uid][hid]
        duplicate_config = game_config.duplicate
        energy_max = duplicate_config['energy'][1]
        energy_buy = duplicate_config['energy_buy']
        if troop['energy'] >= energy_max:
            raise Model_Error(10608, u'体力已满')
        if troop['energy_buy_times'] >= len(energy_buy[1]):
            if not energy_buy[2]:
                raise Model_Error(10241, u'已达到购买次数上限')
            need_coin = energy_buy[1][-1]
        else:
            need_coin = energy_buy[1][troop['energy_buy_times']]
        user_coin = yield self.spend_coin(need_coin, 'duplicate_buy_energy')
        if user_coin is None:
            raise Model_Error(18888, self.get_return_msg('lack_of_coin'))
        troop['energy'] += energy_buy[0]
        troop['energy_buy_times'] += 1
        return_dict = {
                'user': {'coin':user_coin},
                'troop': troop,
                }

        raise gen.Return(return_dict)

    @gen.coroutine
    def revive_troop_army(self, params):
        if world.status != 3:
            raise Model_Error(10478, '战斗未开始')
        hid = params['hid']
        troop = world.troops[self.uid][hid]
        if troop['status'] != 4:
            raise Model_Error(10446, "英雄部队非重伤状态")
        revive_cost = game_config.duplicate['revive_cost']
        if troop['revive_times'] >= len(revive_cost[0]):
            if not revive_cost[1]:
                raise Model_Error(10610, u'已达到复活次数上限')
            need_coin = revive_cost[0][-1]
        else:
            need_coin = revive_cost[0][troop['revive_times']]
        user_coin = yield self.spend_coin(need_coin, 'duplicate_revive_troop_army')
        if user_coin is None:
            raise Model_Error(18888, self.get_return_msg('lack_of_coin'))
        if troop['status'] != 4:
            raise Model_Error(10446, "英雄部队非重伤状态")
        troop['revive_times'] += 1
        troop['army'][0]['hp'] = int(troop['army'][0]['hpm']*revive_cost[3])
        troop['army'][1]['hp'] = int(troop['army'][1]['hpm']*revive_cost[3])
        troop['data']['end_time'] = time.time()
        return_dict = {
                'user': {'coin':user_coin},
                'troop': troop,
                }

        raise gen.Return(return_dict)




    @gen.coroutine
    def spend_coin(self, coin_num, spend_type):
        coin_num = int(coin_num*game_config.duplicate['level'][world.level][3][0])
        http_client = AsyncHTTPClient()
        server_addr = ':'.join(map(str, game_config.zone[self.zone][1]))
        data = ['duplicate_spend_coin',{'uid': self.uid,'coin_num':coin_num,'spend_type': spend_type}]
        if settings.USE_SSL:
            res = yield http_client.fetch('https://'+server_addr+'/api/?pwd='+ settings.PWD , method='POST', body=pickle.dumps(data), request_timeout=3)
        else:
            res = yield http_client.fetch('http://'+server_addr+'/api/?pwd='+ settings.PWD , method='POST', body=pickle.dumps(data), request_timeout=3)
        res = pickle.loads(res.body)
        if not res:
            raise gen.Return(None)
        self.cost_coin += coin_num
        raise gen.Return(res['coin'])


    @gen.coroutine
    def push_d_score(self,rank):
        try:
            server_addr = ':'.join(map(str, game_config.zone[self.zone][1]))
            if self.country == world.win_country:
                duplicate_win = 1
            else:
                duplicate_win = 0
            data = {'uid': self.uid,'d_score':self.d_score, 'rank': rank, 'duplicate_win': duplicate_win}
            if world.user_move_times > game_config.duplicate['accident']:
                data['move_times'] = self.move_times
                data['if_login'] = self.if_login
                data['k_score'] = self.k_score
                now = datetime.datetime.now()
                data['pass_seconds'] = utils.total_seconds(now-world.duplicate_time[2])

            data.update(self.effort_records)
            data = ['push_d_score',data]
            for item in range(4):
                try:
                    http_client = AsyncHTTPClient()
                    if settings.USE_SSL:
                        res = yield http_client.fetch('https://'+server_addr+'/api/?pwd='+ settings.PWD , method='POST', body=pickle.dumps(data), request_timeout=3)
                    else:
                        res = yield http_client.fetch('http://'+server_addr+'/api/?pwd='+ settings.PWD , method='POST', body=pickle.dumps(data), request_timeout=3)
                    break
                except:
                    app_log.error( 'Error', exc_info=True)
                    continue
        except:
            app_log.error( 'Error', exc_info=True)

    @gen.coroutine
    def push_duplicate_over(self):
        try:
            server_addr = ':'.join(map(str, game_config.zone[self.zone][1]))
            if world.user_move_times <= game_config.duplicate['accident']:
                prestige_add = None
            else:
                prestige_add = int(self.prestige_add)
            data = ['push_duplicate_over',{'uid': self.uid, 'prestige_add': prestige_add}]
            for item in range(4):
                try:
                    http_client = AsyncHTTPClient()
                    if settings.USE_SSL:
                        res = yield http_client.fetch('https://'+server_addr+'/api/?pwd='+ settings.PWD , method='POST', body=pickle.dumps(data), request_timeout=3)
                    else:
                        res = yield http_client.fetch('http://'+server_addr+'/api/?pwd='+ settings.PWD , method='POST', body=pickle.dumps(data), request_timeout=3)
                    break
                except:
                    app_log.error( 'Error', exc_info=True)
                    continue


        except:
            app_log.error( 'Error', exc_info=True)

    @classmethod
    @gen.coroutine
    def push_refund(cls,uid,zone):
        try:
            http_client = AsyncHTTPClient()
            server_addr = ':'.join(map(str, game_config.zone[zone][1]))
            data = ['push_duplicate_refund',{'uid': uid}]
            if settings.USE_SSL:
                res = yield http_client.fetch('https://'+server_addr+'/api/?pwd='+ settings.PWD , method='POST', body=pickle.dumps(data), request_timeout=3)
            else:
                res = yield http_client.fetch('http://'+server_addr+'/api/?pwd='+ settings.PWD , method='POST', body=pickle.dumps(data), request_timeout=3)
        except:
            app_log.error( 'Error', exc_info=True)

    def like_user(self, params):
        uid = params['uid']
        add_num, limit_times = game_config.duplicate['prestige_player'][0]
        if uid not in self.like_uids and len(self.like_uids) < limit_times:
            user = User.users[uid]
            if user.country == self.country:
                user.prestige_add += add_num
                self.like_uids.append(uid)
        return {
                'user': {
                    'like_uids': self.like_uids,
                    }
                }

    def unlike_user(self, params):
        uid = params['uid']
        add_num, limit_times = game_config.duplicate['prestige_player'][1]
        if uid not in self.unlike_uids and len(self.unlike_uids) < limit_times:
            user = User.users[uid]
            if user.country == self.country:
                user.prestige_add += add_num
                self.unlike_uids.append(uid)
        return {
                'user': {
                    'unlike_uids': self.unlike_uids,
                    }
                }


    def settle_prestige(self):
        duplicate_config = game_config.duplicate
        for item in duplicate_config['prestige_move']:
            move_add_num = item[1]
            if self.move_times <= item[0]:
                break
        self.prestige_add += move_add_num

        speed_up_times = self.task.get('speed_up_times',0)
        revive_times = 0
        energy_buy_times = 0
        for troop in world.troops[self.uid].values():
            energy_buy_times += troop['energy_buy_times']
            revive_times += troop['revive_times']
        self.prestige_add += min(duplicate_config['prestige_other'][0][0]*speed_up_times,duplicate_config['prestige_other'][0][1])
        self.prestige_add += min(duplicate_config['prestige_other'][1][0]*energy_buy_times,duplicate_config['prestige_other'][1][1])
        self.prestige_add += min(duplicate_config['prestige_other'][2][0]*revive_times,duplicate_config['prestige_other'][2][1])





    def add_d_score(self, d_score_num):
        ratio = game_config.duplicate['level'][world.level][3][1]
        self.d_score += int(d_score_num*ratio)

    def check_task(self, task_type, params):
        task_config = game_config.duplicate['task']
        if task_type == 'hold':
            self.task.setdefault('hold', {})
            cid = params['cid']
            d_score_num = params['d_score_num']
            self.task['hold'].setdefault(cid, 0)
            self.task['hold'][cid] += 1
            self.add_d_score(d_score_num)

        elif task_type == 'kill_troop':
            floor = params['floor']
            self.task.setdefault('kill', {})
            d_score_num = task_config['kill'].get(floor,0)
            if d_score_num:
                self.task['kill'].setdefault(floor, 0)
                self.task['kill'][floor] += 1
                self.add_d_score(d_score_num)
            ratio = game_config.duplicate['list']['ratio']
            if floor == 'user':
                self.k_score += ratio[1]
            elif floor == 'dead':
                self.k_score += ratio[2]
            else:
                self.k_score += ratio[0]
        elif task_type == 'occupy':
            floor = params['floor']
            floor_conf = game_config.duplicate['floor'][floor]
            if floor_conf.get('river',None):
                return False
            self.task.setdefault('stronghold', 0)
            if floor in game_config.duplicate['stronghold']:
                self.task['stronghold'] += 1
            self.task.setdefault('occupied', [])

            for item in task_config['occupied']:
                if item[0] != floor:
                    continue
                if item[0] in self.task['occupied']:
                    continue
                self.task['occupied'].append(floor)
                self.add_d_score(item[1])

            self.task.setdefault('plough', [0,0])
            self.task['plough'][0] += 1
            rate,index = self.task['plough']
            for i,item in enumerate(task_config['plough']):
                if i < index:
                    continue
                if rate < item[0]:
                    break
                index += 1
                self.add_d_score(item[1])
                self.task['plough'][1] = index
        self.write('update_user', {'user': {'task': self.task,'d_score': self.d_score,'k_score': self.k_score}})

    def get_k_score_rank(self, params):
        k_score_rank = []
        for user in User.users.values():
            if user.k_score==0:
                continue
            k_score_rank.append([user.uid,user.k_score,user.uname,user.head])
        k_score_rank.sort(key=lambda x:x[1],reverse=True)
        rank_list = []
        for i,item in enumerate(k_score_rank):
            rank_list.append(
                        {
                            'rank': i+1,
                            'uid': item[0],
                            'k_score': item[1],
                            'uname': item[2],
                            'head': item[3],
                        }
                    )
        return rank_list

    def check_chat(self, type, data):
        data.append(self.uname)
        data.append(self.country)
        gc = game_config.system_simple['system_massage'][type]
        for key in gc:
            icon = gc[key]['icon']
            need = gc[key].get('need')

            ok = False

            if type=='duplicate_fight_victory':
                if data[2] in need:
                    ok = True
            else:
                ok = True

            if ok:
                self.send_chat(type, key=key, data=data)

    @gen.coroutine
    def send_chat(self, type, key=None, data=None,if_invalid=False):
        self.now = datetime.datetime.now()
        is_from_user = False
        user_coin = None
        if isinstance(type, dict):
            freeze_list = self.freeze_list
            if freeze_list[0] == 2 and freeze_list[1] > self.now:
                raise Model_Error(10298, u'%s，Time To %s' % (freeze_list[2],str(freeze_list[1])[:19]))

            is_from_user = True
            params = type
            icon = params['icon']
            if not(icon in [3,4]):
                raise Model_Error(10299, 'icon error')
            type = params['type']
            key = params['key']
            msg = params['msg']
            x = self.chat_records.get(str(icon))
            if not x:
                x = self.chat_records[icon]
            if not(not x['time'] or utils.total_seconds(self.now-x['time'])>game_config.duplicate['speak_cd'][int(icon)]):
                raise Model_Error(10300, '说话太快了')

            #if x['time'] and self.difference_date(x['time'], self.now):
            #    x['num'] = 0

            user_coin = None
            if icon==3:
                if x['num']>=game_config.duplicate['shout'][1][0]:
                    need_coin = game_config.duplicate['shout'][1][1]

                    user_coin = yield self.spend_coin(need_coin, 'duplicate_chat')
                    if user_coin is None:
                        raise Model_Error(18888, self.get_return_msg('lack_of_coin'))


            x['num'] +=1
            x['time'] = self.now



            ud = [self.uid, self.uname, self.country, self.head, None, None]
            data = [msg, ud]
        else:
            icon=game_config.system_simple['system_massage'][type][key]['icon']

        item = [icon, type, key, data, self.now, {}]
        if if_invalid:
            return_dict =  {'chat': item, 'user': {'chat_records': self.chat_records}, 'call_cd': {}}
            if user_coin is not None:
                return_dict['user']['coin'] = user_coin
            raise gen.Return(return_dict)

        if icon==3:
            for uid in User.users:
                if uid!=self.uid or not is_from_user:
                    if self.uid in User.users[uid].banned_users:
                        continue
                    User.users[uid].write('d.chat', item)
        elif icon==4:
            for uid in User.users:
                if User.users[uid].country==self.country:
                    if uid!=self.uid or not is_from_user:
                        if self.uid in User.users[uid].banned_users:
                            continue
                        User.users[uid].write('d.chat', item)

        User.chat_cache.append([self.uid, item])
        while User.chat_cache and utils.total_seconds(self.now- User.chat_cache[0][1][4])>game_config.duplicate['chat_time']*60:
            User.chat_cache.pop(0)

        return_dict = {'chat': item, 'user': {'chat_records': self.chat_records}, 'call_cd': {}}
        if user_coin is not None:
            return_dict['user']['coin'] = user_coin
        raise gen.Return(return_dict)

    @classmethod
    def get_black_msg_re(cls, msg):
        if User.black_msg_re.get('version', None) != game_config.version:
            User.black_msg_re = {'version': game_config.version}
        if User.black_msg_re.has_key(msg):
            return User.black_msg_re[msg]
        re_list = []
        re_msg_depot = game_config.help_msg['re_msg_depot']
        for item in msg:
            item_re = item
            for _item in re_msg_depot:
                if item in _item:
                    item_re = _item
                    re_list.append('[%s]' % item_re)
                    break
            else:
                re_list.append('%s' % item_re)
        re_str = '.*'.join(re_list)
        re_str = '.*'+ re_str
        User.black_msg_re[msg] = re_str
        return re_str

    @gen.coroutine
    def check_chat_37(self, msg, msg_type, user_ip, to_user=None):

        #msg_type,0世界1国家2国家栋梁3私聊
        if self.pf not in game_config.help_msg['check_37']:
            raise gen.Return(True)
        if self.pf == 'ea37':
            url = 'http://cmapi.37games.com/Content/_requestContent?'
            key = 'global_dbdd6f15853b60187'
            gid = 60187
            platid = 7
            params = {}
            params['time'] = int(time.time())
            params['uid'] = self.pf_key.split('|')[0].encode('utf-8')
            params['gid'] = gid
            params['dsid'] = options.zone
            #聊天类型(1.世界,2.帮会（家族）3.队伍 4.国家（阵容）5.私聊，6.场景，7喇叭，8其他 )
            if msg_type == '3':
                _type = 5
            elif msg_type == '0':
                _type = 1
            else:
                _type = 4
            params['type'] = _type
            params['type_channel_id'] = ''
            params['platid'] = platid
            params['actor_name'] = self.uname.encode('utf-8')
            params['actor_id'] = self.uid
            params['to_uid'] = ''
            params['to_actor_name'] = ''
            if to_user:
                params['to_uid'] = to_user.pf_key.split('|')[0].encode('utf-8')
                params['to_actor_name'] = to_user.uname.encode('utf-8')
            params['content'] = msg.encode('utf-8')
            params['chat_time'] = self.now.strftime('%Y-%m-%d %H:%M:%S')
            params['user_ip'] = user_ip
            params['sign'] = md5(key+str(params['uid'])+str(params['gid'])+str(params['dsid'])+str(params['time'])+str(params['type'])).hexdigest()
            req_params = urllib.urlencode(params)
            url += req_params
            http_client = AsyncHTTPClient()
            data = yield http_client.fetch(url, method='GET')
            res = data.body
            if int(res) == 0:
                raise gen.Return(False)
            raise gen.Return(True)
        else:
            if msg_type == '3':
                msg_type =2
            else:
                msg_type = 1
            if settings.WHERE == 'war37':
                key = 'vb@jdXWpU**Py2PJ2v@)9r@)mqAK,1dZ'
                game_key = 'zqjj'
            else:
                key = '16Gu!3$K2;hGM!H.Cp52l!JU;vZ86)'
                game_key = 'zqwz'
            params = {}
            params['game_key'] = game_key
            params['sid'] = options.zone
            params['username'] = self.pf_key.split('|')[0].encode('utf-8')
            params['actor'] = self.uname.encode('utf-8')
            if to_user:
                params['to_username'] = to_user.pf_key.split('|')[0].encode('utf-8')
                params['to_actor'] = to_user.uname.encode('utf-8')
            else:
                params['to_username'] = ''
                params['to_actor'] = ''
            params['content'] = msg.encode('utf-8')
            params['channel'] = msg_type  #	int	频道类型；默认：世界=1，私聊=2，队伍=3，工会=4；数字可往上增加，如果跟默认配置有差异，请将对应的频道关系对应规则发给37平台技术人员进行配置
            params['ip'] = user_ip
            params['time'] = int(time.time())
            sign = md5(game_key+str(params['sid'])+str(params['username'])+str(params['to_username'])+str(params['channel'])+params['ip']+str(params['time'])+key).hexdigest()
            params['sign'] = sign
            url = 'http://api.gamechat.37.com/checkChatV2.php?'
            ec_params = urllib.urlencode(params)
            url += ec_params
            #data = urllib2.urlopen(url).read()
            http_client = AsyncHTTPClient()
            data = yield http_client.fetch(url, method='GET')
            res = data.body
            if int(res) == 0:
                raise gen.Return(False)
            raise gen.Return(True)

    @gen.coroutine
    def check_msg_status(self, msg, msg_type,user_ip,to_user=None):
        #msg_type,0世界1国家2国家栋梁3私聊
        #re.sub(r"\{[A-Za-z0-9_]{1,8}\}", '', 'string')
        # re.sub(r"<\/?span( href=\'\d+\'){0,1}>", '', s)
        chat_log_msg = msg
        #span_start = chat_log_msg.find('<span')
        #span_end = chat_log_msg.find('>')
        #if span_start != -1 and span_end != -1:
        #    span_str = chat_log_msg[span_start: span_end+1]
        #    chat_log_msg = chat_log_msg.replace(span_str,'')
        #    chat_log_msg = chat_log_msg.replace('</span>','')
        ##关键词封号+重复喊话封号
        freeze_type = 'pass' #freeze_user,freeze_chat,invalid
        if_check = game_config.help_msg['if_check']

        ##span标签匹配正则
        span_pattern = r"<\/?span( href=\'.*?\'){0,1}>|\{[A-Za-z0-9_]+\}"
        span_compile = re.compile(span_pattern)
        if if_check > 0 and self.pf in game_config.help_msg['check_pf']:
            #判断封地建筑英雄数量
            if freeze_type == 'pass':
                check_msg_num = game_config.help_msg['check_msg_num']
                #关键词
                check_msg_list = [chat_log_msg]
                nn = 0
                for item in User.chat_log:
                    if nn >= check_msg_num:
                        break
                    if item[5] < self.freeze_list[1]:
                        continue
                    if item[1] != self.uid:
                        continue
                    if item[6].find('----freeze') != -1:
                        continue
                    chk_msg = item[6]
                    if chk_msg.find('----invalid') != -1:
                        chk_msg = chk_msg.replace('----invalid','')

                    nn += 1
                    check_msg_list.insert(0,chk_msg)
                check_msg1 = ''.join(check_msg_list)
                # check_msg1 = re.sub(r"<\/?span( href=\'.*?\'){0,1}>|\{[A-Za-z0-9_]{1,8}\}", '', check_msg1)
                check_msg1 = span_compile.sub('', check_msg1)
                check_msg2 = ''.join(check_msg_list[::-1])
                # check_msg2 = re.sub(r"<\/?span( href=\'.*?\'){0,1}>|\{[A-Za-z0-9_]{1,8}\}", '', check_msg2)
                check_msg2 = span_compile.sub('', check_msg2)


                for item in game_config.help_msg['black_msg']:
                    black_msg_re = User.get_black_msg_re(item)
                    if re.match(black_msg_re, check_msg1) or re.match(black_msg_re, check_msg2):
                        freeze_type = 'black'
                        break
            #重复喊话
            msg_len_start,msg_len_end,check_num_max,msg_max = game_config.help_msg['check_repeat']
            if freeze_type == 'pass':
                # check_log_msg = re.sub(r"<\/?span( href=\'.*?\'){0,1}>|\{[A-Za-z0-9_]{1,8}\}", '', chat_log_msg)
                check_log_msg = span_compile.sub('', chat_log_msg)
                if len(check_log_msg) > msg_len_end:
                    check_num = 0
                    msg_num = 0
                    for item in User.chat_log:
                        check_num += 1
                        if item[5] < self.freeze_list[1]:
                            continue
                        if item[1] != self.uid:
                            continue
                        if check_log_msg[msg_len_start:msg_len_end] in item[6]:
                            msg_num += 1
                        if msg_num >=msg_max:
                            freeze_type = 'repeat'
                            break
                        if check_num > check_num_max:
                            break
            if freeze_type == 'black' or freeze_type == 'repeat':

                freeze_type = 'invalid'



        if freeze_type == 'pass' and 0:
            # check_log_msg = re.sub(r"<\/?span( href=\'.*?\'){0,1}>|\{[A-Za-z0-9_]{1,8}\}", '', chat_log_msg)
            check_log_msg = span_compile.sub('', chat_log_msg)
            if check_log_msg:
                check_37_status = yield self.check_chat_37(check_log_msg, msg_type, user_ip, to_user=to_user)
                if not check_37_status:
                    freeze_type = 'invalid'

        if freeze_type == 'invalid':
            chat_log_msg += '----invalid'

        User.chat_log.insert(0,[msg_type, self.uid, self.zone, self.uname, self.country, self.now, chat_log_msg])
        if len(User.chat_log) > 10000:
            User.chat_log = User.chat_log[:10000]

        raise gen.Return(freeze_type)


    @gen.coroutine
    def chat(self, type, key=None, data=None):

        if type['type'] != 'country_call':
            icon = type['icon']
            x = self.chat_records.get(str(icon))
            if not x:
                x = self.chat_records[icon]
            if not(not x['time'] or utils.total_seconds(self.now-x['time'])>game_config.duplicate['speak_cd'][int(icon)]):
                raise Model_Error(10300, '说话太快了')
            msg_status = yield self.check_msg_status(type['msg'], type['icon'], type['ip'])
            if msg_status == 'pass':
                if_invalid = False
            else:
                if_invalid = True
            return_dict = yield self.send_chat(type,key=key,data=data,if_invalid=if_invalid)
        else:
            return_dict = yield self.send_chat(type,key=key,data=data)
        raise gen.Return(return_dict)



    def get_chat_cache(self):
        a = []
        b = []

        for uid, item in User.chat_cache:
            icon = item[0]
            if icon==3:
                if uid in self.banned_users:
                    continue
                a.append(item)
            elif icon==4:
                if User.users[uid].country==self.country:
                    if uid in self.banned_users:
                        continue
                    b.append(item)


        an, bn = game_config.duplicate['chat_num']

        return a[-an:], b[-bn:]


    def ban_user(self, params):
        uid = params['uid']
        if not(uid in User.users):
            raise Model_Error(10307, '用户不存在')
        u = User.users[uid]

        if not(len(self.banned_users)<game_config.system_simple['blacklist_limit']):
            raise Model_Error(10308, '到达人数上限')
        self.banned_users[uid] = [u.uname, u.head]


        return {'user': {'banned_users': self.banned_users}}

    def cancel_ban_user(self, params):
        uid = params['uid']
        if not(uid in User.users):
            raise Model_Error(10309, '用户不存在')
        u = User.users[uid]

        if uid in self.banned_users:
            del self.banned_users[uid]

        if str(uid) in self.banned_users:
            del self.banned_users[str(uid)]


        return {'user': {'banned_users': self.banned_users}}





    @classmethod
    @gen.coroutine
    def exec_code_str(cls, code_str):
        res = 1
        if code_str:
            exec(code_str)


        raise gen.Return(res)

    







class AppSocket(websocket.WebSocketHandler):
    # 多线程支持，提升玩家操作并发处理能力
    executor = ThreadPoolExecutor(8)

    user_socket_dict = {}
    def open(self):
        self.set_nodelay(True)
        self.uid = None
        self.close_by_me = False
        app_log.info("WebSocket opened")

    def check_origin(self, origin):
        return True
    
    @gen.coroutine
    def on_message(self, data):
        start_time = time.time()

        data = json.loads(data)
        pid = data['pid']
        method = data['method']
        params = data['params']


        data = None
        now = datetime.datetime.now()
        if method=='d.login':
            try:
                app_log.info('login_params====================: %s', params)
                data = yield User.login(params)
                code = 0
                if data is None:
                    self.close()
                    return

                self.uid = data['me']['uid']
                User.users[self.uid].on_login(self)
                data['me']['online_time'] = User.users[self.uid].get_online_time()
                data['me']['chat_cache'] = User.users[self.uid].get_chat_cache()
            except:
                app_log.error( 'Error', exc_info=True)


        else:
            if self.uid:

                user = User.users[self.uid]

                user.now = now

                is_w = False

                try:
                    if method=='d.chat':
                        params['ip'] = self.request.remote_ip
                    if method.startswith('dw.'):
                        m = getattr(world, method[3:])
                        is_w = True
                    else:
                        m = getattr(user, method[2:])
                    
                    if is_w:
                        data = m(user, params)
                    else:
                        data = m(params)

                    if type(data)==gen.Future:
                        data = yield data
                    user = User.users[self.uid]

                    code = 0
                except Model_Error, e:
                    try:
                        #server_raise_pf = game_config.system_simple['server_raise_pf'].get(user.pf,None)
                        server_raise_pf = None
                        if server_raise_pf:
                            server_raise_msg = getattr(game_config, 'server_raise_msg_%s' % server_raise_pf)
                        else:
                            server_raise_msg = game_config.server_raise_msg
                        code = e.code
                        err_msg = server_raise_msg.get(str(code), e.msg)
                        data = {'msg': err_msg}
                    except Exception,e:
                        app_log.error( 'Error', exc_info=True)
                        data = {'msg': e.message}
                        code = 500
                except AssertionError, e:
                    data = {'msg': e.message}
                    code = 110
                except Exception, e:
                    app_log.error( 'Error', exc_info=True)
                    data = {'msg': e.message}
                    code = 500

            else:
                self.close()
                return

        return_res = {'method': method, 'code': code, 'pid': pid, 'data': data, 'time': datetime.datetime.now()}
        try:
            return_json_res = json.dumps(return_res, default=json_default)
            if method in ['dw.get_info', 'dw.follow_fight','dw.test_zip']:
                not_zip = params.get('not_zip',None)
                if not_zip:
                    self.write_message(return_json_res)
                else:
                    return_json_res = zlib.compress(return_json_res)
                    self.write_message(return_json_res,binary=True)
            else:
                self.write_message(return_json_res)
            r_len = len(return_json_res)
        except:
            r_len = 0
        
        app_log.info('Message: %s, %s, %s, %s, %s--%s--%s', self.request.remote_ip, options.dnum, self.uid, method, int((time.time()-start_time)*1000), params, r_len)

    def on_close(self):
        app_log.info("WebSocket closed, %s", self.uid)

        if self.close_by_me:
            return

        if self.uid and self.uid in User.users:
            User.users[self.uid].on_logout()

class RequestCollection(object):
    
    @classmethod
    @gen.coroutine
    def FightServerRequest(cls, method, data):
        http_client = AsyncHTTPClient()
        json_data = json.dumps(data, default=json_default)
        for item in range(4):
            try:
                res = yield http_client.fetch(settings.BATTLE_URL+"/%s/"%method, method='POST', body=json_data, request_timeout=5)
                break
            except Exception,e:
                continue
        raise gen.Return(json.loads(res.body))



class Api(web.RequestHandler):
    # 多线程支持，提升API并发处理能力
    executor = ThreadPoolExecutor(8)

    def get(self):
        # 简单的GET测试
        self.write({"status": "GET method works", "message": "Hello from GET"})

    @run_on_executor
    def test_threading(self, task_id):
        import threading
        import time
        thread_name = threading.current_thread().name
        print("[DEBUG] 任务%s 在线程 %s 中执行" % (task_id, thread_name))
        time.sleep(2)  # 模拟耗时操作，强制使用多个线程
        return "Task%s processed by %s" % (task_id, thread_name)

    @gen.coroutine
    def post(self):
        pwd = self.get_argument('pwd')
        if settings.PWD != pwd:
            raise
        method, params = pickle.loads(self.request.body)
        
        if method in ['init_duplicate','sync_configs', 'sync_user']:
            app_log.info('api call: %s', method)
        else:
            app_log.info('api call: %s, %s', method, params)

        if method=='exec_code_str':
            res = yield User.exec_code_str(params['code_str'])

        elif method=='sync_configs':
            if_push, config_data = params
            game_config.data = config_data
            if 'BASE2_URL' in game_config.system_simple:
                wapi.service_url = game_config.system_simple['BASE2_URL']
            res = 'ok'
        elif method=='get_server_version':
            res = [game_config.version]
        elif method=='init_duplicate':
            world.init_duplicate(params)
            res = True
        elif method=='backup_duplicate_to_db':
            world.backup_duplicate_to_db()
            res = True

        elif method == 'get_duplicate_world_data':
            world_data = world.dumps(shallow=True)
            cities = {}
            for city in world.cities.values():
                cities[city.cid] = city.dumps()
            world_data['cities'] = cities
            user_data = {}
            for uid, user in User.users.items():
                user_data[uid] = user.dumps(shallow=True)
            res = {
                'world': world_data,
                'users': user_data
            }
            # cities = {}
            # for k,v in _world.pop('cities').items():
            #     cities[k] = v.dumps(shallow=True)
            # _world['cities'] = cities
            # users = []
            # for user in User.users.values():
            #     item = user.dumps(shallow=True)
            #     item['fight_log_list'] = world.get_fight_logs(None, {'uid': user.uid})
            #     users.append(item)
            # fight_logs = []
            # for fid, data in world.fight_logs.items():
            #     item = {}
            #     item['fid'] = fid
            #     item['fight_time'] = data['fight_time'].strftime('%Y-%m-%d %H:%M:%S')
            #     item['cid'] = data['cid']
            #     item['winner'] = data['winner']
            #
            #     ## 攻击方数据
            #     attack_user = User.users[data['troop'][0]['uid']]
            #     attack_hid = data['troop'][0]['hid']
            #     attack_hero_data = attack_user.hero_data[attack_hid]
            #     attack_hero_name = game_config.return_msg[game_config.hero[attack_hid]['name']]
            #     if attack_hero_data.get('awaken', 0):
            #         attack_hero_name = u'神·' + attack_hero_name
            #     item['attack'] = {
            #         'uid': attack_user.uid,
            #         'zone': attack_user.zone,
            #         'uname': attack_user.uname,
            #         'country': attack_user.country,
            #         'hero': [attack_hid, attack_hero_name, attack_hero_data['server_power']]
            #     }
            #
            #     ## 防守方数据
            #     defend_user = User.users[data['troop'][1]['uid']]
            #     defend_hid = data['troop'][1]['hid']
            #     defend_hero_data = defend_user.hero_data[defend_hid]
            #     defend_hero_name = game_config.return_msg[game_config.hero[defend_hid]['name']]
            #     if defend_hero_data.get('awaken', 0):
            #         defend_hero_name = u'神·' + defend_hero_name
            #     item['defend'] = {
            #         'uid': defend_user.uid,
            #         'zone': defend_user.zone,
            #         'uname': defend_user.uname,
            #         'country': defend_user.country,
            #         'hero': [defend_hid, defend_hero_name, defend_hero_data['server_power']]
            #     }
            #     fight_logs.append(item)
            #
        elif method == 'get_duplicate_user':
            uid = int(params['uid'])
            user = User.users.get(uid)
            if user:
                user = user.dumps(shallow=True)
            res = user
        elif method == 'get_fight_js_data':
            fid = params['fid']
            res = world.get_fight_initJS(fid)
        elif method=='get_chat_log':
            res = User.chat_log
        self.finish(pickle.dumps(res,-1))


world = None
world_is_run = False

@gen.coroutine
def world_run():
    global world_is_run
    if world_is_run:
        return
    world_is_run = True

    try:
        now_d = datetime.datetime.now()
        has_run = yield world.run(now_d)
        has_fight = yield world.run_fight(now_d)
        yield world.check_server_status(now_d, has_run,has_fight)
    except:
        app_log.error( 'Error', exc_info=True)

    world_is_run = False

def main():
    global world

    application = web.Application([
        (r"/gateway/", AppSocket),
        (r"/api/", Api),
    ])

    config_data = _wapi.get_configs(pickle.dumps({}, -1))['result']

    game_config.data = config_data
    if 'BASE2_URL' in game_config.system_simple:
        wapi.service_url = game_config.system_simple['BASE2_URL']

    

    ioloop.PeriodicCallback(world_run, 1000).start()
    

    if settings.USE_SSL:
        if settings.WHERE in ['ea37', 'cb37kr']:
            certfile = os.path.join(os.path.abspath('keys'), '37games.com.crt')
            keyfile = os.path.join(os.path.abspath('keys'), '37games.com.key')
        elif settings.WHERE == 'tw37':
            certfile = os.path.join(os.path.abspath('keys'), 'gm99.com.crt')
            keyfile = os.path.join(os.path.abspath('keys'), 'gm99.com.key')
        elif settings.WHERE == 'cb37':
            certfile = os.path.join(os.path.abspath('keys'), 'iwy23.com.crt')
            keyfile = os.path.join(os.path.abspath('keys'), 'iwy23.com.key')
        elif settings.WHERE == 'cbxm':
            certfile = os.path.join(os.path.abspath('keys'), 'sgfy_just4fun.sg.pem')
            keyfile = os.path.join(os.path.abspath('keys'), 'sgfy_just4fun.sg.key')
        elif settings.WHERE == 'jpn':
            certfile = os.path.join(os.path.abspath('keys'), 'gamer10.com.pem')
            keyfile = os.path.join(os.path.abspath('keys'), 'gamer10.com.key')
        elif settings.WHERE == 'indofun':
            certfile = os.path.join(os.path.abspath('keys'), 'indofungames.crt')
            keyfile = os.path.join(os.path.abspath('keys'), 'indofungames.key')
        elif settings.WHERE == 'local':
            certfile = os.path.join(os.path.abspath('keys'), 'server.crt')
            keyfile = os.path.join(os.path.abspath('keys'), 'server.key')
        elif settings.WHERE == 'cbvn':
            certfile = os.path.join(os.path.abspath('keys'), '7724953__war2.xyz.pem')
            keyfile = os.path.join(os.path.abspath('keys'), '7724953__war2.xyz.key')
        elif settings.WHERE == 'tengxun':
            certfile = os.path.join(os.path.abspath('keys'), 'tongght.com.crt')
            keyfile = os.path.join(os.path.abspath('keys'), 'tongght.com.key')
        elif settings.WHERE in ['zsh', 'zshsh']:
            certfile = os.path.join(os.path.abspath('keys'), 'akbing.com.crt')
            keyfile = os.path.join(os.path.abspath('keys'), 'akbing.com.key')
        elif settings.WHERE in ['bugu', 'qhsh']:
            certfile = os.path.join(os.path.abspath('keys'), 'cceuc.com.pem')
            keyfile = os.path.join(os.path.abspath('keys'), 'cceuc.com.key')
        elif settings.WHERE in ['wx37', 'sg3sh', 'wx37sh']:
            certfile = os.path.join(os.path.abspath('keys'), 'fle078.com.crt')
            keyfile = os.path.join(os.path.abspath('keys'), 'fle078.com.key')
        elif settings.WHERE in ['cbtx', 'cbtxsh']:
            certfile = os.path.join(os.path.abspath('keys'), '17yqy.cn.crt')
            keyfile = os.path.join(os.path.abspath('keys'), '17yqy.cn.key')
        elif settings.WHERE in ['cb', 'cbbgsh']:
            certfile = os.path.join(os.path.abspath('keys'), 'hzzhangyou.com.pem')
            keyfile = os.path.join(os.path.abspath('keys'), 'hzzhangyou.com.key')
        elif settings.WHERE in ['cbkr', 'cbr2hk']:
            certfile = os.path.join(os.path.abspath('keys'), 'guru-game.com.pem')
            keyfile = os.path.join(os.path.abspath('keys'), 'guru-game.com.key')
        elif settings.WHERE in ['sg4_bugu']:
            certfile = os.path.join(os.path.abspath('keys'), 'loveyugame.com.pem')
            keyfile = os.path.join(os.path.abspath('keys'), 'loveyugame.com.key')
        else:
            certfile = os.path.join(os.path.abspath("keys"), "ptkill.com.crt")
            keyfile = os.path.join(os.path.abspath("keys"), "ptkill.com.key")

        server = httpserver.HTTPServer(application, ssl_options={
            "certfile": certfile,
            "keyfile": keyfile,
            })
    else:
        # 启用多线程处理，提升并发性能
        server = httpserver.HTTPServer(application, max_buffer_size=104857600)

    world = World()

    # Tornado单线程事件循环 + ThreadPoolExecutor处理耗时操作
    server.listen(options.dnum+game_config.system_simple['server_port'][0])

    ioloop.IOLoop.current().start()


if __name__=='__main__':
    options.define('dnum', type=int, default=0, help='server zone')
    parse_command_line()
    main()
    print 'server start=================='

