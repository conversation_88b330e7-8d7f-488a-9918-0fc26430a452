#-*- coding: utf-8 -*-
import os, sys, time, datetime, random, json, struct, urllib2
import math
import re
import zlib
from tornado import web, websocket, gen, ioloop, autoreload, httpserver
from tornado.options import parse_command_line, options
from tornado.log import app_log
import c<PERSON>ickle as pickle
from jsonrpc.proxy import ServiceProxy
from jsonrpc.proxy2 import ServiceProxy as ServiceProxy2
from tornado.httpclient import AsyncHTTPClient
from concurrent.futures import ThreadPoolExecutor
from tornado.concurrent import run_on_executor
from tornado.curl_httpclient import CurlError
from sqlalchemy import create_engine
from sqlalchemy import *
from common import utils
from copy import deepcopy as copy

AsyncHTTPClient.configure("tornado.curl_httpclient.CurlAsyncHTTPClient")

sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../', 'trunk/llol/src'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../', 'trunk/game_lib'))
import settings
from django.core.management import setup_environ
setup_environ(settings)
import gc as ggc
import subprocess
from game_lib.common.tencentcloud.send_sms import QcloudSms
from django.core.cache import get_cache
ggc.disable()

DB_ECHO = settings.DB_ECHO
DB_ENCODING = 'utf-8'

DBENGINE = 'mysql://%s:%s@%s:%s/%s?charset=utf8mb4' % settings.DB_ACCOUNT_PASSWORD['shards']['1']['master']

# 优化服务器管理数据库连接
db_config = getattr(settings, 'DB_CONNECTION_CONFIG', {})
db_engine = create_engine(
    DBENGINE,
    echo=DB_ECHO,
    encoding=DB_ENCODING,
    pool_size=db_config.get('pool_size', 15),      # 管理服务适中连接数
    max_overflow=db_config.get('max_overflow', 25),
    pool_recycle=db_config.get('pool_recycle', 3600),
    pool_pre_ping=db_config.get('pool_pre_ping', True),
    pool_timeout=db_config.get('pool_timeout', 30),
    connect_args=db_config.get('connect_args', {
        'connect_timeout': 10,
        'read_timeout': 30,
        'write_timeout': 30,
        'charset': 'utf8mb4',
        'autocommit': True,
    })
)

cache = get_cache(settings.CACHE_BACKEND)

_wapi = ServiceProxy(settings.BASE2_URL[0])
wapi = ServiceProxy2(settings.BASE2_URL)

def json_default(obj):
    if isinstance(obj, datetime.datetime):
        return {"$datetime": obj.strftime('%Y-%m-%d %H:%M:%S')}
    if isinstance(obj, datetime.date):
        return {"$date": obj.strftime('%Y-%m-%d')}
def json_object_hook(dct):
    if "$datetime" in dct:
        return datetime.datetime.strptime(dct['$datetime'], '%Y-%m-%d %H:%M:%S')
    if "$date" in dct:
        return datetime.date(*map(int, (dct["$date"].split('-'))))
    return dct

def notify_active_error(zone_id):
    """
    新区激活失败通知
    :param zone_id:
    :return:
    """
    try:
        template_id = '784782'
        template_params = [settings.WHERE, zone_id]
        QcloudSms.send_sms(ServerManage.alert_tel, template_id, template_params)
    except Exception as e:
        app_log.exception(e)

def notify_user_num(zone_id, user_num):
    """
    超过区服最大人数限制通知
    :param zone_id:
    :param user_num:
    :return:
    """
    try:
        template_id = '784795'
        template_params = [settings.WHERE, zone_id, user_num]
        QcloudSms.send_sms(ServerManage.alert_tel, template_id, template_params)
    except Exception as e:
        app_log.exception(e)

def notify_auto_stop(zone_group, stop_time):
    """
    自动开服计划停止通知
    :param zone_id:
    :param user_num:
    :return:
    """
    try:
        template_id = '784809'
        template_params = [settings.WHERE, zone_group, stop_time.strftime("%Y-%m-%d %H:%M")]
        QcloudSms.send_sms(ServerManage.alert_tel, template_id, template_params)
    except Exception as e:
        app_log.exception(e)

def notify_free_memory(zone, ip, available):
    """
    可用内存不足通知
    :return:
    """
    try:
        template_id = '787728'
        template_params = [settings.WHERE, ip.split('.')[0], zone, u'内存不足，当前空闲内存：%.2fG' % available*1.0/1024]
        QcloudSms.send_sms(ServerManage.alert_tel, template_id, template_params)
    except Exception as e:
        app_log.exception(e)

def notify_backup_failed(zone):
    """
    备份失败通知
    :return:
    """
    try:
        template_id = '978284'
        template_params = ['%s(%s)' % (settings.WHERE, ServerManage.local_ip.split('.')[0]), zone]
        QcloudSms.send_sms(ServerManage.alert_tel, template_id, template_params)
    except Exception as e:
        app_log.exception(e)

def notify_zone_use_memory(zone, ip, use_memory):
    """
    单区进程占用内存超过15G通知
    :return:
    """
    try:
        template_id = '1613314'
        template_params = [settings.WHERE, zone, ip.split('.')[0], str(use_memory)]
        QcloudSms.send_sms(ServerManage.alert_tel, template_id, template_params)
    except Exception as e:
        app_log.exception(e)

class ServerManage(object):

    executor = ThreadPoolExecutor(1)

    zone_config = None
    config_version = None
    zone_list = None
    ## 自动化开服配置
    auto_open_config = None

    # 当前服务器中运行中的区服
    runing_server_zone_list = []

    # 区服当前玩家总数
    zone_user_num = {}
    # 报警接收人
    alert_tel = ['18101060123']

    # 自动开服计划停止通知时间
    notify_auto_stop = {}
    # 内存报警通知时间
    notify_memory = {}
    notify_memory_cd = 60  #内存报警时间间隔(分钟)
    # 单服人数报警通知时间
    notify_user_num = {}
    notify_user_num_cd = 60  #人数报警时间间隔(分钟)

    # 自动开服的区服ID
    auto_start_zones = []

    # 允许启动新服的最小剩余内存 M
    allow_start_min_memory = 10240

    maintain = {}


    backup_all_cd = 3600  #全备时间间隔
    backup_all_time = 0

    gc_collect_hours = [4]
    gc_collect_hour = None  #gc清理时间

    if sys.platform == 'win32':
        ip_file = open('D:\workspace\sg3\ip.ip', 'r')
    else:
        ip_file = open('/data/ip.ip', 'r')
    local_ip = ip_file.read().strip()
    ip_file.close()

    backup_failed_times = {}  #备份失败次数
    zone_use_memory = {}  #单区内存占用过高通知

    @classmethod
    def check_zone_list(cls):
        result = _wapi.get_server_manage_config()['result']
        if result is None:
            app_log.error('check_zone_list Error!!!')
        cls.zone_config = result['zone_config']
        cls.config_version = result['config_version']
        cls.auto_open_config = result['auto_open_config']
        cls.alert_tel = result['alert_tel']
        cls.zone_list = sorted(result['zone_config'].items(), key=lambda x:x[1][2], reverse=True)
        cls.maintain = result['maintain']

    @classmethod
    def call_server_api(cls, zone, method, params):
        data = [method, params]
        if settings.USE_SSL:
            return urllib2.urlopen('https://'+':'.join(map(str, cls.zone_config[zone][1]))+'/api/?pwd='+settings.PWD, pickle.dumps(data),timeout=600).read()
        else:
            return urllib2.urlopen('http://'+':'.join(map(str, cls.zone_config[zone][1]))+'/api/?pwd='+settings.PWD, pickle.dumps(data),timeout=600).read()

    @classmethod
    def get_server_config_version(cls,zone):
        res = cls.call_server_api(zone, 'get_server_status', {})
        res = pickle.loads(res)
        return res[0]

    @classmethod
    def user_backup(cls, zone, if_all=0, if_honour_done=0, if_collect=0):
        res = cls.call_server_api(zone, 'backup_user', {'if_all':if_all,'if_honour_done': if_honour_done, 'if_collect': if_collect})
        return json.loads(res)

    @classmethod
    def fight_log_backup(cls, zone, if_all=0):
        res = cls.call_server_api(zone, 'backup_fight_log', {'if_all':if_all})
        return False

    @classmethod
    def world_backup(cls, zone):
        res = cls.call_server_api(zone, 'backup_world', {})
        return False

    @classmethod
    @run_on_executor
    def backup_server(cls):
        cls.check_zone_list()
        now_t = time.time()
        now = datetime.datetime.now()
        if now_t - cls.backup_all_time > cls.backup_all_cd:
            backup_all = 1
            cls.backup_all_time = now_t
        else:
            backup_all = 0
        if now.hour != cls.gc_collect_hour and now.hour in cls.gc_collect_hours:
            gc_collect = 1
        else:
            gc_collect = 0
        try:
            zone_group = None
            for zone,v in cls.zone_list:
                try:
                    if cls.local_ip != v[1][0]:
                        continue
                    app_log.info('server_backup_zone_start: %09s', zone)
                    zone_group = v[9]

                    zone_t = time.time()
                    #app_log.info('check_server_config: %s', zone)
                    server_config_version = cls.get_server_config_version(zone)
                    if not settings.SUBTEST and server_config_version != cls.config_version:
                        app_log.info('refresh_server_config: %s', zone)
                        urllib2.urlopen(settings.BASE_URL+'/refresh_server_config/?pwd=%s&zone=%s' % (settings.PWD,zone)).read()
                    backup_data = cls.user_backup(zone, backup_all, if_collect=gc_collect)
                    cls.fight_log_backup(zone, backup_all)
                    cls.world_backup(zone)
                    if zone not in cls.runing_server_zone_list:
                        cls.runing_server_zone_list.append(str(zone))
                    app_log.info('server_backup_zone_done: %09s, backup_num: %09s, if_all: %s, if_collect: %s, use_time: %s', zone, backup_data['backup_num'], backup_all, gc_collect, int((time.time()-zone_t)*1000))
                    cls.zone_user_num[zone] = backup_data['user_num']
                    if v[8] == 0:
                        cls.get_zone_use_memory(zone)
                    if cls.backup_failed_times.get(zone, 0) > 0:
                        cls.backup_failed_times[zone] = 0
                except Exception as e:
                    s = cls.start_zone_process(zone, v)
                    now = datetime.datetime.now()
                    if s:
                        continue
                    if now <= v[2]:
                        continue
                    if cls.maintain['full_maintain'] == 0 or zone in cls.maintain['zone_list']:
                        continue
                    if settings.WHERE not in ['local', 'cb_localsh']:
                        cls.backup_failed_times.setdefault(zone, 0)
                        cls.backup_failed_times[zone] += 1
                        if cls.backup_failed_times[zone] >= 5:
                            notify_backup_failed(zone)
                            cls.backup_failed_times[zone] = 0
            if zone_group:
                ## 自动开服检查
                cls.check_auto_start_server(zone_group)
                ## 最新区人数检查
                cls.check_last_zone_user_num(zone_group)
            cls.gc_collect_hour = now.hour
            app_log.info('runing_server_zone_list: %s, %s', cls.runing_server_zone_list, int((time.time()-now_t)*1000))
        except:
            app_log.error('Error', exc_info=True)

    @classmethod
    def get_zone_use_memory(cls, zone):
        """
        获取区服进程所占内存
        :param zone:
        :return:
        """
        try:
            if settings.SUBTEST:
                _command = "ps aux | grep python | grep zone | grep -v grep | grep -w %s | grep -w zone=%s" % (settings.WHERE, zone)
            else:
                _command = "ps aux | grep python | grep zone | grep -v grep | grep -w zone=%s" % zone
            subp = subprocess.Popen(_command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            output = subp.communicate()[0]
            parse_info = output.split()
            if parse_info:
                use_memory = int(parse_info[5])/1024
                if use_memory >= 15360:
                    if settings.WHERE not in ['local', 'cb_localsh']:
                        cls.zone_use_memory.setdefault(zone, 0)
                        if cls.zone_use_memory[zone] == 0:
                            notify_zone_use_memory(zone, cls.local_ip, use_memory)
                        cls.zone_use_memory[zone] += 1
                        if cls.zone_use_memory[zone] >= 2:
                            cls.zone_use_memory[zone] = 0
        except Exception as e:
            app_log.exception(e)


    @classmethod
    def _has_zone_process(cls, zone):
        """
        本机是否存在区服进程
        :param zone:
        :return:
        """
        if settings.SUBTEST:
            _command = 'ps aux | grep python | grep zone | grep -v grep | grep -w %s | grep -w zone=%s' % (settings.WHERE, zone)
        else:
            _command = 'ps aux | grep python | grep zone | grep -v grep | grep -w zone=%s' % zone
        subp = subprocess.Popen(_command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        output = subp.communicate()[0]
        return True if output else False

    @classmethod
    def check_server_free_mem(cls, zone):
        """
        检查本机空闲内存
        :return:
        """
        now = datetime.datetime.now()
        data = cls._get_server_memory()
        available = data['available']
        if available > cls.allow_start_min_memory:
            return True
        else:
            if not settings.SUBTEST:
                last_notify_at = cls.notify_memory.get(zone)
                if not last_notify_at or last_notify_at + datetime.timedelta(minutes=cls.notify_memory_cd) < now:
                    notify_free_memory(zone, cls.local_ip, available)
                    cls.notify_memory[zone] = now
            return False


    @classmethod
    def _get_server_memory(cls):
        subp = subprocess.Popen('free -m', shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        output = subp.communicate()[0]
        data = output.splitlines()
        parse_info = data[1].split()
        if len(data) == 3:
            available = parse_info[-1]
        else:
            available = int(parse_info[3]) + int(parse_info[5]) + int(parse_info[6])
        return {'total': int(parse_info[1]), 'available': int(available)}


    @classmethod
    def start_zone_process(cls, zone, zone_config=None):
        """
        启动区服进程
        :param zone:
        :param zone_config:
        :return:
        """
        if not zone_config:
            cls.check_zone_list()
            zone_config = cls.zone_list[zone]
        now = datetime.datetime.now()
        #if zone in cls.runing_server_zone_list:
        #    return False
        if zone in MergeZone.merge_zones:
            return False
        if cls._has_zone_process(zone):
            return False
        if now + datetime.timedelta(minutes=30) < zone_config[2]:
            return False
        if not cls.check_server_free_mem(zone) and not settings.SUBTEST:
            return False
        if settings.SUBTEST:
            _command = 'python server.py --zone=%s --log-file-prefix=/data/logs/python/%s/service/log%s >/dev/null 2>&1 &' % (zone, settings.WHERE, zone)
        else:
            _command = 'python server.py --zone=%s --log-file-prefix=/data/logs/python/sg3/service/log%s >/dev/null 2>&1 &' % (zone, zone)
        subp = subprocess.Popen(_command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        error = subp.communicate()[1]
        if error:
            app_log.error('Start Zone[{zone}] Failed: {msg}'.format(zone=zone, msg=error))
        else:
            app_log.info('Start Zone Success: %s', zone)
        cls.runing_server_zone_list.append(str(zone))
        return True

    @classmethod
    def kill_zone_process(cls, zone, backup=True):
        """
        杀掉区服进程
        :param zone:
        :param backup:
        :return:
        """
        try:
            if backup:
                cls.user_backup(zone, 1)
                cls.fight_log_backup(zone, 1)
                cls.world_backup(zone)
            if settings.SUBTEST:
                _command = "ps aux | grep python | grep zone | grep -v grep | grep -w %s | grep -w zone=%s | kill -9 `awk -F' ' '{print $2}'`" % (settings.WHERE, zone)
            else:
                _command = "ps aux | grep python | grep zone | grep -v grep | grep -w zone=%s | kill -9 `awk -F' ' '{print $2}'`" % zone
            os.system(_command)
            app_log.info('>>>>>>>>>>>>>>>kill_zone: %s', zone)
        except Exception as e:
            app_log.exception(e)


    @classmethod
    def _get_last_running_zone(cls, zone_group):
        """
        获取最新开区的区服ID
        :param zone_group: 区服分区
        :return:
        """
        now = datetime.datetime.now()
        zone_ids = []
        for zone, v in cls.zone_list:
            if v[8]:
                continue
            if v[9] != zone_group:
                continue
            zone_ids.append(int(zone))
        return max(zone_ids) if zone_ids else None

    @classmethod
    def check_last_zone_user_num(cls, zone_group):
        """
        检查最新开区的玩家数
        :param zone_group:
        :return:
        """
        now = datetime.datetime.now()
        last_zone = cls._get_last_running_zone(zone_group)
        if not last_zone:
            return
        zone_user_num = cls.zone_user_num.get(str(last_zone), 0)
        auto_config = cls.auto_open_config[zone_group]
        if auto_config and zone_user_num > auto_config['server_user_num']:
            last_notify_at = cls.notify_user_num.get(last_zone)
            if not last_notify_at or last_notify_at + datetime.timedelta(minutes=cls.notify_user_num_cd) < now:
                notify_user_num(last_zone, zone_user_num)
                cls.notify_user_num[last_zone] = now

    @classmethod
    def check_auto_start_server(cls, zone_group):
        """
        自动开服检查
        同一台机器不能分配到多个区服分组中
        :return:
        """
        now = datetime.datetime.now()
        if not zone_group:
            return
        auto_config = cls.auto_open_config[zone_group]
        if not auto_config:
            return
        if not auto_config['active']:
            return
        ## 自动开服计划停止前一个小时通知相关人员
        auto_stop_notify_time = auto_config['auto_stop_at'] - datetime.timedelta(hours=1)
        if now >= auto_stop_notify_time and now < auto_config['auto_stop_at']:
            last_notify_time = cls.notify_auto_stop.get(zone_group)
            _is_send = False
            if not last_notify_time:
                _is_send = True
            else:
                if last_notify_time < auto_stop_notify_time:
                    _is_send = True
            if _is_send:
                notify_auto_stop(zone_group, auto_config['auto_stop_at'])
                cls.notify_auto_stop[zone_group] = now

        if auto_config['auto_stop_at'] < now:
            return
        last_zone = cls._get_last_running_zone(zone_group)
        if not last_zone:
            return
        zone_config = cls.zone_config[str(last_zone)]
        zone_user_num = cls.zone_user_num.get(str(last_zone), 0)

        ## 禁止自动开服时间段
        if now.hour >= auto_config['ban_open_time'][0] and now.hour < auto_config['ban_open_time'][1]:
            return
        is_open = False
        ## 开服时间超过最大时间间隔立即开服
        if zone_config[2] + datetime.timedelta(hours=auto_config['time_interval']) < now:
            is_open = True
            open_time = zone_config[2] + datetime.timedelta(hours=auto_config['time_interval']) + datetime.timedelta(minutes=auto_config.get('open_delay', 60))
        ## 导入量
        elif zone_user_num >= auto_config['import_user_num']:
            is_open = True
            open_time = datetime.datetime(*now.timetuple()[:5]) + datetime.timedelta(minutes=auto_config.get('open_delay', 60))

        if is_open:
            auto_start_zone = str(last_zone + 1)
            if auto_start_zone in cls.auto_start_zones or auto_start_zone in cls.runing_server_zone_list:
                return
            if open_time < now:
                open_time = datetime.datetime(*now.timetuple()[:5]) + datetime.timedelta(minutes=auto_config.get('open_delay', 60))
            params = {
                'zone_id': auto_start_zone,
                'open_time': open_time
            }
            result = _wapi.active_zone(pickle.dumps(params, -1))['result']
            if result['status'] != 'success':
                app_log.error('Auto Start Zone Error: %s,%s' % (params, result['msg']))
                notify_active_error(params['zone_id'])
            else:
                app_log.info('Auto Start Zone Success: %s' % params)
                cls.auto_start_zones.append(auto_start_zone)


class MergeZone(object):

    merges = {}
    merge_zones = []

    def __init__(self, merge_id, merge_config, merge_zones):
        self.merge_id = merge_id
        self.merge_config = merge_config
        self.merge_zones = merge_zones
        MergeZone.merges[merge_id] = []
        if self.merge_config['game_id'] == 'cb':
            self.country_ftask = [['11009', '11007'], ['21009', '21007'], ['31009', '31007']]
        else:
            self.country_ftask = [['144', '146'], ['31', '62'], ['350', '353']]
        self.source_merge_times = {}  # 本次合服涉及的原区合服次数变更

    def _get_zone_merge_times(self, zone):
        if zone.startswith('h') and '_' in zone:
            merge_times = int(zone.split('_')[0][1:])
        else:
            merge_times = 0
        return merge_times

    def get_msg_info(self, from_zone, pf, game_config):
        merge_times = str(self._get_zone_merge_times(from_zone) + 1)
        if settings.WHERE in ['kr', 'cb37kr']:
            msg_config = game_config['server_raise_msg_kr']['merge_msg']
        elif settings.WHERE in ['hk', 'tw37', 'cbhk']:
            if pf == 'r2g_xm':
                msg_config = game_config['server_raise_msg']['merge_msg']
            else:
                msg_config = game_config['server_raise_msg_tw']['merge_msg']
        elif settings.WHERE == 'ea37':
            pf_lan = game_config['system_simple']['server_raise_pf'].get(pf)
            if pf_lan:
                msg_config = game_config['server_raise_msg_%s' % pf_lan]['merge_msg']
            else:
                msg_config = game_config['server_raise_msg']['merge_msg']
        elif settings.WHERE in ['vn', 'cbvn']:
            msg_config = game_config['server_raise_msg_viet']['merge_msg']
        elif settings.WHERE == 'th':
            msg_config = game_config['server_raise_msg_th']['merge_msg']
        elif settings.WHERE == 'jpn':
            msg_config = game_config['server_raise_msg_ja']['merge_msg']
        elif settings.WHERE == 'indofun':
            msg_config = game_config['server_raise_msg_bahasa']['merge_msg']
        else:
            msg_config = game_config['server_raise_msg']['merge_msg']
        return msg_config[merge_times]

    def run(self):
        MergeZone.merge_zones.extend(self.merge_zones.keys())
        now = datetime.datetime.now()
        game_config = _wapi.get_configs(pickle.dumps({}, -1))['result']
        for zone in self.merge_zones.keys():
            try:
                zone_config = ServerManage.zone_config[zone]
                if zone_config[1][0] != ServerManage.local_ip:
                    continue
                # 备份
                time.sleep(random.randint(5, 10))
                start_time = time.time()
                if ServerManage._has_zone_process(zone):
                    backup_data = ServerManage.user_backup(zone, if_all=1, if_honour_done=1)
                    ServerManage.fight_log_backup(zone, if_all=1)
                    ServerManage.world_backup(zone)
                    app_log.info('>>>>>>>>>>>>>>>merge_backup_done: %09s, backup_num: %09s, use_time: %s', zone, backup_data['backup_num'], int((time.time()-start_time)*1000))

                # 杀掉进程
                ServerManage.kill_zone_process(zone, backup=False)

            except Exception as e:
                app_log.exception(e)
        time.sleep(30)
        conn = db_engine.connect()
        for zone in self.merge_zones.keys():
            try:
                zone_config = ServerManage.zone_config[zone]
                if zone_config[1][0] != ServerManage.local_ip:
                    continue
                # 合服
                time.sleep(random.randint(10, 60))
                app_log.info('###############merge_start: %s', zone)
                self._join_user_to_db(zone, conn, game_config)
                app_log.info("###############merge_done: %s", zone)
                MergeZone.merges[self.merge_id].append(zone)
            except Exception as e:
                app_log.exception(e)
        try:
            ## 更新本次合服涉及的原区合服次数
            for k,v in self.source_merge_times.items():
                conn.execute(text("update zones set op_merge_times=:op_merge_times where zone_id=:zone_id"),
                             {'zone_id': k, 'op_merge_times': v})
        except Exception as e:
            app_log.exception(e)
        conn.close()

    @classmethod
    @gen.coroutine
    def update_merge_finish_zones(cls):
        for k,v in cls.merges.items():
            if not v:
                continue
            zones = copy(v)
            params = {
                    'merge_id': k,
                    'zones': zones
                    }
            app_log.info("*****update_merge_finish_start: %s %s", k, v)
            for i in range(5):
                try:
                    data = yield wapi.update_merge(pickle.dumps(params, -1))
                    if data['status'] == 'success':
                        for zone in zones:
                            if zone not in cls.merges[k]:
                                continue
                            cls.merges[k].remove(zone)
                        app_log.info("*****update_merge_finish_end_success: %s %s", k, v)
                    else:
                        app_log.info("*****update_merge_finish_end_error: %s", data)
                    break
                except CurlError as e:
                    app_log.info('Retry update_merge Request[%s]: %s times', k, i)
                    yield gen.sleep(10)
                    continue
            for zone in zones:
                if zone not in cls.merges[k]:
                    continue
                cls.merges[k].remove(zone)
                

    def _join_user_to_db(self, from_zone, conn, game_config):
        uid_add = settings.UIDADD
        buildinglv_per = self.merge_config['buildinglv_per']
        base_reward = self.merge_config['base_reward']
        buildinglv_dict = {}
        #'':[[18,1],[12,0.6],[6,0.2],[1,0]],
        #n = 40
        n = max([int(i) for i in game_config['home']['building001']['levelup'].keys()]) + 1
        for item in buildinglv_per:
            for i in range(item[0],n):
                buildinglv_dict[i] = item[1]
            n = item[0]

        now = datetime.datetime.now()
        merge_value = self.merge_zones[from_zone]

        from_user_table = utils.get_table_name('user',from_zone,settings.USER_TABLE_NUM)
        db_user = conn.execute("select * from %s where zone='%s'" % (from_user_table, from_zone)).fetchall()

        # 赛季结算
        db_system_data = conn.execute("select * from system_data where zone='%s'" % from_zone).fetchone()
        if not db_system_data:
            system_data = {}
        else:
            system_data =zlib.decompress(db_system_data.data)
            system_data = eval(system_data)
        honour = system_data.get('honour')
        honour_start_time = honour.get('start_time', datetime.datetime.now())
        honour_end_time = datetime.datetime.now()
        honour_rank_dict = {}
        rank = 1
        for item in honour.get('rank', []):
            honour_rank_dict[item[0]] = rank
            rank += 1
        from_merge_times = game_config['zone'][from_zone][8]
        if from_merge_times >= 6:
            del_building_lv = 24
        else:
            del_building_lv = 12

        to_zones = set()
        n = 0
        for item in db_user:
            #try:
            n += 1
            if n % 10000 == 0:
                app_log.info("%s join user num: %s", from_zone, n)
            user_data = zlib.decompress(item.user_data)
            if settings.USE_SERIALIZE == 'json':
                user_data = json.loads(user_data, object_hook=json_object_hook)
            else:
                user_data = eval(user_data)
            if not user_data:
                continue
            if user_data['country'] not in [0,1,2]:
                self.del_user_login_zone(from_zone, user_data['uid'], conn)
                continue
            if not user_data['online_log']['login_time']:
                self.del_user_login_zone(from_zone, user_data['uid'], conn)
                continue
            building_lv = user_data['home']['building001']['lv']
            if user_data['records']['pay_money'] == 0 and building_lv < del_building_lv and user_data['online_log']['login_time']+datetime.timedelta(days=7) < now:
                self.del_user_login_zone(from_zone, user_data['uid'], conn)
                continue
            if user_data.get('player_robot', 0) == 1:
                self.del_user_login_zone(from_zone, user_data['uid'], conn)
                continue
            rewards = merge_value[user_data['country']]['reward']
            to_zone = merge_value[user_data['country']]['to_zone']
            user_data['country'] = merge_value[user_data['country']]['to_country']
            cache_key = '%smerge_zone_uname_%s' % (settings.CACHE_PRE, to_zone)
            uname_list = cache.get(cache_key, [])
            to_user_table = utils.get_table_name('user',to_zone,settings.USER_TABLE_NUM)
            uname = user_data['uname']

            if uname is None:
                self.del_user_login_zone(from_zone, user_data['uid'], conn)
                continue
            gift_dict = {}
            while 1:
                if uname not in uname_list:
                    break
                uname_n = re.findall('\d+$', uname)
                if uname_n:
                    uname_n = int(uname_n[0]) + 1
                    uname = re.split('\d+$',uname)[0]
                    uname = u'%s%s' % (uname,uname_n)
                else:
                    uname = u'%s1' % uname
                user_data['uname'] = uname
                gift_dict.update(game_config['help_msg']['change_uname_gift'])

            uname_list.append(uname)
            cache.set(cache_key, uname_list, timeout=60*60*2)
            gift_dict.update(base_reward)
            for hid,hvl in user_data['hero'].items():
                hvl['work'] = None
            user_data['task']['country'] = []
            for x,y in rewards.items():
                yy = int(y*buildinglv_dict[building_lv])
                if yy:
                    gift_dict.setdefault(x,0)
                    gift_dict[x] += yy
            for k in settings.MERGE_DELETE_ATTR['user_del_key_list']:
                if user_data.has_key(k):
                    del user_data[k]
            user_data['msg']['usr'] = []
            msg_name, msg_info = self.get_msg_info(from_zone, user_data['pf'], game_config)
            gift_list = [msg_name, msg_info, gift_dict, now, 0]
            user_data['msg']['sys'].insert(0, gift_list)
            for k in settings.MERGE_DELETE_ATTR['user_del_records_key_list']:
                if user_data['records'].has_key(k):
                    del user_data['records'][k]
            for k in settings.MERGE_DELETE_ATTR['user_del_total_records_key_list']:
                if user_data['total_records'].has_key(k):
                    del user_data['total_records'][k]
            uid = int(user_data['uid']) % settings.UIDADD
            if int(user_data['uid'])/settings.UIDADD > 0:
                old_zone = int(user_data['uid'])/settings.UIDADD
            else:
                old_zone = from_zone

            new_uid = uid_add * int(old_zone) + uid
            user_data['uid'] = new_uid
            user_data['ftask'] = {}

            if 'country_ftask' in game_config['system_simple']:
                merge_times = self._get_zone_merge_times(to_zone)
                if game_config['system_simple'].get('use_merge_maps') == 1:
                    try:
                        merge_maps = game_config['system_simple']['merge_maps'][merge_times]
                    except IndexError:
                        merge_maps = game_config['system_simple']['merge_maps'][-1]
                else:
                    merge_maps = -1
                if merge_maps == -1:
                    country_ftask = game_config['system_simple']['country_ftask']
                else:
                    country_ftask = game_config['system_simple']['country_ftask_maps'][merge_maps]
            else:
                country_ftask = self.country_ftask

            for city_id in country_ftask[user_data['country']]:
                user_data['ftask'][city_id] = [-1,0,0]

            # 赛季结算
            if user_data['honour_hero']:
                exp_max_hero = user_data['honour_hero'].get('exp_max_hero', None)
                if exp_max_hero:
                    exp_max_hero[1] = user_data['honour_hero'][exp_max_hero[0]]['lv']
                    total_lv = user_data['honour_hero'].get('total_lv',0)
                    x,y,z = game_config['honour']['honour_reward_%s' % from_merge_times]
                    reward_num = min((total_lv*x),y)
                    log_dict = {
                            'start_time': honour_start_time,
                            'end_time': honour_end_time,
                            'total_lv': total_lv,
                            'reward_num': reward_num,
                            'rank': honour_rank_dict.get(user_data['uid'],None),
                            'exp_max_hero': exp_max_hero,
                            'status': 0,
                            'merge_times': from_merge_times,
                            'task_done_num': user_data['honour_task'].get('done_num',0),
                            }
                    user_data['honour_log'].append(log_dict)
                    user_data['honour_hero'] = {}
                    user_data['honour_task'] = {}


            #insert to db
            if settings.USE_SERIALIZE == 'json':
                str_user = json.dumps(user_data, default=json_default)
            else:
                str_user = repr(user_data)
            str_user =zlib.compress(str_user)
            db_uid = str(new_uid)
            power = user_data['power']

            conn.execute(text("insert into %s (uid,zone,power,user_data) values (:uid,:zone,:power,:user_data)" % to_user_table),{'uid':db_uid,'zone': to_zone,'power': power, 'user_data': str_user})

            ## 更新合区指向
            self.update_user_login_zone(from_zone, new_uid, to_zone, conn)
            to_zones.add(to_zone)
            #except:
            #    self.del_user_login_zone(from_zone, user_data['uid'], conn)
            #    continue
        for i in to_zones:
            self.update_system_data(from_zone, i, conn)
        
    def del_user_login_zone(self, from_zone, uid, mysql_conn):
        try:
            if int(uid)/settings.UIDADD > 0:
                from_zone = int(uid)/settings.UIDADD
            else:
                from_zone = int(from_zone)
            uid = uid % settings.UIDADD
            res = mysql_conn.execute("select zone_login from user_zone where uid=%s" % uid).fetchone()
            zone_login = pickle.loads(str(res.zone_login))
            if str(from_zone) in zone_login:
                del zone_login[str(from_zone)]
                mysql_conn.execute(text("update user_zone set zone_login=:zone_login where uid=:uid"),{'uid':uid, 'zone_login': pickle.dumps(zone_login)})
        except:
            app_log.error('MergeZone delete zone_login error: %s,%s' % (uid, from_zone))

    def update_user_login_zone(self, from_zone, uid, to_zone, mysql_conn):
        try:
            if int(uid)/settings.UIDADD > 0:
                from_zone = int(uid)/settings.UIDADD
            else:
                from_zone = int(from_zone)
            uid = uid % settings.UIDADD
            user_zone = mysql_conn.execute("select zone_login from user_zone where uid=%s" % uid).fetchone()
            zone_login = pickle.loads(str(user_zone.zone_login))
            if str(from_zone) in zone_login:
                zone_login[str(from_zone)]['merge_zone'] = to_zone
                mysql_conn.execute(text("update user_zone set zone_login=:zone_login where uid=:uid"),{'uid':uid, 'zone_login': pickle.dumps(zone_login)})
            self.source_merge_times[str(from_zone)] = self._get_zone_merge_times(to_zone)
        except Exception as e:
            app_log.exception(e)
            app_log.error('MergeZone update zone_login error: %s,%s,%s' % (uid, from_zone, to_zone))
            
    def update_system_data(self, from_zone, to_zone, mysql_conn):
        try:
            to_system_data = mysql_conn.execute("select * from system_data where zone='%s'" % to_zone).fetchone()
            if not to_system_data:
                db_system_data = mysql_conn.execute("select * from system_data where zone='%s'" % from_zone).fetchone()
                if not db_system_data:
                    system_data = {}
                else:
                    system_data =zlib.decompress(db_system_data.data)
                    system_data = eval(system_data)
                    
                res = {}
                res['festival'] = system_data.get('festival', {})
                res = repr(res)
                res =zlib.compress(res)
                mysql_conn.execute(text(
                    "insert into system_data (zone, data) VALUES (:zone,:data) ON DUPLICATE KEY UPDATE data=:data"),
                            {'zone': to_zone, 'data': res})
        except Exception as e:
            app_log.exception(e)
            app_log.error('MergeZone update_system_data error: %s,%s' % (from_zone, to_zone))



class Api(web.RequestHandler):
    executor = ThreadPoolExecutor(1)
    @gen.coroutine
    def post(self):
        pwd = self.get_argument('pwd')
        if settings.PWD != pwd:
            raise Exception('Error Request')
        method, params = pickle.loads(self.request.body)
        
        if method == 'get_server_version':
            res = [ServerManage.config_version]
        elif method == 'start_merge_zone':
            self.start_merge_zone(params)
            res = 'ok'
        elif method == 'get_merge_finish_zones':
            merge_id = params['merge_id']
            res = MergeZone.merges.get(merge_id, [])
        elif method == 'stop_zone_process':
            zone_id = params['zone_id']
            ServerManage.kill_zone_process(zone_id)
            res = 'ok'
        self.finish(pickle.dumps(res,-1))


    @run_on_executor
    def start_merge_zone(self, params):
        merge_id = params['merge_id']
        merge_config = params['merge_config']
        merge_zones = params['merge_zones']
        merge_obj = MergeZone(merge_id, merge_config, merge_zones)
        merge_obj.run()

def main():

    application = web.Application([
        (r"/api/", Api),
    ])

    if settings.WHERE in ['local', 'cb_localsh']:
        backup_cd = 60
    else:
        backup_cd = 600
    try:
        ioloop.IOLoop.current().run_sync(ServerManage.backup_server)
    except:
        pass
    ioloop.PeriodicCallback(ServerManage.backup_server, backup_cd*1000).start()
    ioloop.PeriodicCallback(MergeZone.update_merge_finish_zones, 60*1000*1).start()

    if settings.USE_SSL:
        if settings.WHERE in ['ea37', 'cb37kr']:
            certfile = os.path.join(os.path.abspath('keys'), '37games.com.crt')
            keyfile = os.path.join(os.path.abspath('keys'), '37games.com.key')
        elif settings.WHERE == 'tw37':
            certfile = os.path.join(os.path.abspath('keys'), 'gm99.com.crt')
            keyfile = os.path.join(os.path.abspath('keys'), 'gm99.com.key')
        elif settings.WHERE == 'cb37':
            certfile = os.path.join(os.path.abspath('keys'), 'iwy23.com.crt')
            keyfile = os.path.join(os.path.abspath('keys'), 'iwy23.com.key')
        elif settings.WHERE == 'cbxm':
            certfile = os.path.join(os.path.abspath('keys'), 'sgfy_just4fun.sg.pem')
            keyfile = os.path.join(os.path.abspath('keys'), 'sgfy_just4fun.sg.key')
        elif settings.WHERE == 'jpn':
            certfile = os.path.join(os.path.abspath('keys'), 'gamer10.com.pem')
            keyfile = os.path.join(os.path.abspath('keys'), 'gamer10.com.key')
        elif settings.WHERE == 'indofun':
            certfile = os.path.join(os.path.abspath('keys'), 'indofungames.crt')
            keyfile = os.path.join(os.path.abspath('keys'), 'indofungames.key')
        elif settings.WHERE == 'local':
            certfile = os.path.join(os.path.abspath('keys'), 'server.crt')
            keyfile = os.path.join(os.path.abspath('keys'), 'server.key')
        elif settings.WHERE == 'cbvn':
            certfile = os.path.join(os.path.abspath('keys'), '7724953__war2.xyz.pem')
            keyfile = os.path.join(os.path.abspath('keys'), '7724953__war2.xyz.key')
        elif settings.WHERE == 'tengxun':
            certfile = os.path.join(os.path.abspath('keys'), 'tongght.com.crt')
            keyfile = os.path.join(os.path.abspath('keys'), 'tongght.com.key')
        elif settings.WHERE in ['zsh', 'zshsh']:
            certfile = os.path.join(os.path.abspath('keys'), 'akbing.com.crt')
            keyfile = os.path.join(os.path.abspath('keys'), 'akbing.com.key')
        elif settings.WHERE in ['bugu', 'qhsh']:
            certfile = os.path.join(os.path.abspath('keys'), 'cceuc.com.pem')
            keyfile = os.path.join(os.path.abspath('keys'), 'cceuc.com.key')
        elif settings.WHERE in ['wx37', 'sg3sh', 'wx37sh']:
            certfile = os.path.join(os.path.abspath('keys'), 'fle078.com.crt')
            keyfile = os.path.join(os.path.abspath('keys'), 'fle078.com.key')
        elif settings.WHERE in ['cbtx', 'cbtxsh']:
            certfile = os.path.join(os.path.abspath('keys'), '17yqy.cn.crt')
            keyfile = os.path.join(os.path.abspath('keys'), '17yqy.cn.key')
        elif settings.WHERE in ['cb', 'cbbgsh']:
            certfile = os.path.join(os.path.abspath('keys'), 'hzzhangyou.com.pem')
            keyfile = os.path.join(os.path.abspath('keys'), 'hzzhangyou.com.key')
        elif settings.WHERE in ['cbkr', 'cbr2hk']:
            certfile = os.path.join(os.path.abspath('keys'), 'guru-game.com.pem')
            keyfile = os.path.join(os.path.abspath('keys'), 'guru-game.com.key')
        elif settings.WHERE in ['sg4_bugu']:
            certfile = os.path.join(os.path.abspath('keys'), 'loveyugame.com.pem')
            keyfile = os.path.join(os.path.abspath('keys'), 'loveyugame.com.key')
        else:
            certfile = os.path.join(os.path.abspath("keys"), "ptkill.com.crt")
            keyfile = os.path.join(os.path.abspath("keys"), "ptkill.com.key")


        server = httpserver.HTTPServer(application, ssl_options={
            "certfile": certfile,
            "keyfile": keyfile,
            })
    else:
        server = httpserver.HTTPServer(application)
        
    server.listen(settings.SERVER_MANAGE_PORT)

    ioloop.IOLoop.current().start()


if __name__=='__main__':
    options.define('port', type=int, default=3352, help='server port')
    parse_command_line()
    main()
    print 'server start=================='

