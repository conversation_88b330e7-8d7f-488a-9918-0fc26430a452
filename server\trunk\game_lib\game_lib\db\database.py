#-*- coding: utf-8 -*-
import datetime
from sqlalchemy import *
from sqlalchemy.databases import mysql
#from django.conf import settings
import settings
from django.core.cache import get_cache

#-------------------------------------------------------------------------------
DB_ECHO = settings.DB_ECHO
DB_ENCODING = 'utf-8'

#------------------------------------------------------------------------------
ShardMetas = {}
CacheServers = {}
#------------------------------------------------------------------------------
def _get_meta(db_account):
    DBENGINE = 'mysql://%s:%s@%s:%s/%s?charset=utf8' % db_account
    # 优化数据库连接池配置
    # pool_size: 连接池大小，根据服务器CPU核心数调整
    # max_overflow: 最大溢出连接数，防止连接泄漏
    # pool_recycle: 连接回收时间，防止MySQL连接超时
    # pool_pre_ping: 连接前检查，确保连接有效性
    DB = create_engine(
        DBENGINE,
        echo=DB_ECHO,
        encoding=DB_ENCODING,
        pool_size=20,           # 增加连接池大小
        max_overflow=30,        # 限制最大溢出连接
        pool_recycle=3600,      # 1小时回收连接
        pool_pre_ping=True,     # 连接前检查
        pool_timeout=30,        # 获取连接超时时间
        connect_args={
            'connect_timeout': 10,  # 连接超时
            'read_timeout': 30,     # 读取超时
            'write_timeout': 30,    # 写入超时
        }
    )
    meta = MetaData(DB)
    return meta, DB
#------------------------------------------------------------------------------
def _get_cache(cache_backend):
    return get_cache(cache_backend)
#------------------------------------------------------------------------------
for k,v in settings.DB_ACCOUNT_PASSWORD['shards'].items():
    master, db_engine = _get_meta(v['master'])
    if v['slaver'] == v['master']:
        slaver = master
    else:
        slaver, _ = _get_meta(v['slaver'])
    ShardMetas[k] = {'master': master, 'slaver': slaver, 'db_engine': db_engine}
    c_cache = _get_cache(v['cache_backend'])
    CacheServers[k] = c_cache
    
#------------------------------------------------------------------------------
meta = ShardMetas['1']['master']
user_table_num = 23

#------------------------------------------------------------------------------
#CataLog,分了57个表,57为质数没有其他意义

if settings.DB_ACCOUNT_PASSWORD.has_key('catalog'):
    CacheServers['catalog'] = _get_cache(settings.DB_ACCOUNT_PASSWORD['catalog']['cache_backend'])
    catalog_table_num = 57
    catalog_master = _get_meta(settings.DB_ACCOUNT_PASSWORD['catalog']['master'])
    if settings.DB_ACCOUNT_PASSWORD['catalog']['master'] == settings.DB_ACCOUNT_PASSWORD['catalog']['slaver']:
        catalog_slaver = catalog_master
    else:
        catalog_slaver = _get_meta(settings.DB_ACCOUNT_PASSWORD['catalog']['slaver'])
    CataLog_Metas = {'master': master, 'slaver': slaver}

    def get_catalog_table(table_name):
        _table = Table(table_name, catalog_master,
            Column('uid', String(32), primary_key = True, nullable = False, autoincrement = False),	#用户id
            
            Column('shard', String(10), nullable = False),
            mysql_engine='InnoDB'
        )
        return _table

    for i in xrange(0, catalog_table_num):
        __table = get_catalog_table('catalog_%d' % i)
        exec('catalog_table_%d = __table' % i)



#------------------------------------------------------------------------------



#####################################################################
InstallCode_table = Table('install_code', meta,
    Column('phone_ip', String(255), primary_key = True, nullable = False),	#用户id
    Column('code', String(64),index=True), 
    Column('add_time', DateTime ,index = True),  ##注册时间
    Column('phone_data', BLOB),
    mysql_engine='InnoDB',
)
#Waiter
Waiter_table = Table('waiter', meta,
    Column('waiter_id', Integer, primary_key = True, nullable = False),	#用户id
    Column('waiter_pwd', String(64)), 
    Column('waiter_lv', Integer),
    Column('admin_user', String(64)), 
    Column('zone_list', BLOB),
    Column('zone_log', BLOB),
    Column('status', Integer),
    mysql_engine='InnoDB',
)
#UserZone
UserZone_table = Table('user_zone', meta,
    Column('uid', Integer, primary_key = True, nullable = False, autoincrement = 1000),	#用户id
    Column('pf', String(64)),
    Column('channel', String(64)),
    Column('pf_key', String(64), index=True, nullable=False, unique=True),
    Column('pf_pwd', String(64)), 
    Column('user_code', String(64)),     #身份证号
    Column('tel', String(64)),     #电话
    Column('www', Integer, index=True),     #是否官方注册
    Column('add_time', DateTime ,index = True),  ##注册时间
    Column('login_time', DateTime ,index = True),  ##登陆时间
    Column('phone_id', String(255)),     #imei,idfa
    Column('zones', String(255)), 
    Column('max_lv', Integer),
    Column('pay_money', Integer),
    Column('member', Integer),
    Column('user_ip', String(255)),
    Column('freeze_status', Integer),
    Column('ucoin', Integer),
    Column('zone_login', BLOB),
    Column('inviter', String(32)),  #邀请人
    Column('player', Integer, index=True),   #是否是陪玩账号， 0否， 1是
    Column('player_id', String(32), index=True),  #陪玩模板ID
    mysql_engine='InnoDB',
)

#User
User_table = Table('user', meta,
    Column('uid', String(64), primary_key = True, nullable = False),	#用户id
    Column('zone', String(8), primary_key = True, nullable = False),	#分区id
    Column('power', Integer, nullable=True, default=0),
    Column('user_data', BLOB),
    mysql_engine='InnoDB',
)

#fight_log
FightLog_table = Table('fight_log', meta,
    Column('uid', String(64), primary_key = True, nullable = False),	#用户id
    Column('zone', String(8), primary_key = True, nullable = False),	#分区id
    Column('log_data', BLOB),
    mysql_engine='InnoDB',
)
for item in range(settings.USER_TABLE_NUM):
    #User
    User_table = Table('user_%s' % item, meta,
        Column('uid', String(64), primary_key = True, nullable = False),	#用户id
        Column('zone', String(8), primary_key = True, nullable = False),	#分区id
        Column('power', Integer, nullable=True, default=0),
        Column('user_data', BLOB),
        mysql_engine='InnoDB',
    )

    #fight_log
    FightLog_table = Table('fight_log_%s' % item, meta,
        Column('uid', String(64), primary_key = True, nullable = False),	#用户id
        Column('zone', String(8), primary_key = True, nullable = False),	#分区id
        Column('log_data', BLOB),
        mysql_engine='InnoDB',
    )

#Guild
Guild_table = Table('guild', meta,
    Column('gid', Integer, primary_key = True, nullable = False),	#用户id
    Column('zone', String(8), primary_key = True, nullable = False),	#分区id
    Column('guild_data', BLOB),
    mysql_engine='InnoDB',
)

#country_club
CountryClub_table = Table('country_club', meta,
    Column('zone', String(8), primary_key = True, nullable = False),	#分区id
    Column('club_data', BLOB),
    mysql_engine='InnoDB',
)

#world
World_table = Table('world', meta,
    Column('zone', String(8), primary_key = True, nullable = False),	#分区id
    Column('world_data', BLOB),
    mysql_engine='InnoDB',
)

#system_data
System_data_table = Table('system_data', meta,
    Column('zone', String(8), primary_key = True, nullable = False),	#分区id
    Column('data', BLOB),
    mysql_engine='InnoDB',
)

#pk_yard
System_data_table = Table('pk_yard', meta,
    Column('zone', String(8), primary_key = True, nullable = False),	#分区id
    Column('data', BLOB),
    mysql_engine='InnoDB',
)

#pk_arena
System_data_table = Table('pk_arena', meta,
    Column('zone', String(8), primary_key = True, nullable = False),	#分区id
    Column('data', BLOB),
    mysql_engine='InnoDB',
)

#pk_robot
Pk_robot_table = Table('pk_robot', meta,
    Column('zone', String(8), primary_key = True, nullable = False),	#分区id
    Column('robot_data', BLOB),
    mysql_engine='InnoDB',
)


#bug反馈
BugMsg_table = Table('bug_msg', meta,
    Column('uid', String(64), primary_key = True, nullable = False), #uid|zone
    Column('zone', String(8), index=True, nullable = False),
    Column('time', DateTime ,index = True),##提交时间
    Column('content',String(640)), ##用户提交内容
    Column('status',Integer,nullable=False,index=True), ##状态
    Column('data',BLOB), 
    mysql_engine='InnoDB',
)

#国家公告记录
"""
CREATE TABLE `country_notice` (
`zone_cid` varchar(16) NOT NULL,
`update_time` datetime,
`content` varchar(640),
PRIMARY KEY (`zone_cid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
"""
CountryNotice_table = Table('country_notice', meta,
    Column('zone_cid', String(16), primary_key=True, nullable=False),
    Column('update_time', DateTime, index=True),  ##提交时间
    Column('content', String(640)),  ##公告内容
    mysql_engine='InnoDB',
)

#冻结机器码
FreezePhone_table = Table('freeze_phone', meta,
    Column('phone_id', String(255), primary_key=True, nullable=False),
    mysql_engine='InnoDB',
)

#冻结记录
FreezeLog_table = Table('freeze_log', meta,
    Column('uid', String(64), primary_key = True, nullable = False), #uid|zone
    Column('zone', String(8), index=True, nullable = False),
    Column('uname',String(64)), 
    Column('status',Integer,nullable=False,index=True), ##状态
    Column('freeze_time', DateTime ,index = True),    ##冻结截止时间
    Column('freeze_msg',String(640)), ## 冻结原因
    Column('admin_user',String(64)), 
    Column('admin_time', DateTime ,index = True),    
    mysql_engine='InnoDB',
)

#冻结记录历史
FreezeHistory_table = Table('freeze_history', meta,
    Column('id', Integer, primary_key=True, nullable=False, autoincrement=True),
    Column('uid', String(64), index=True, nullable=False),
    Column('zone', String(8), index=True, nullable=False),
    Column('status',Integer, nullable=False, index=True), ##状态 0：未冻结, 1: 冻结登录， 2：冻结发言
    Column('freeze_time', DateTime, index=True),    ##冻结截止时间
    Column('freeze_msg',String(640)), ## 冻结原因
    Column('admin_user',String(64)),
    Column('admin_time', DateTime, index=True),
    mysql_engine='InnoDB',
)


#邮件发奖
GiftMsg_table = Table('gift_msg', meta,
    Column('uid', Integer, primary_key = True, nullable = False),	#用户id
    Column('data',BLOB), 
    mysql_engine='InnoDB',
)




#后台管理员操作日志
AdminLog_table = Table('admin_log', meta,
    Column('id', Integer, primary_key = True, nullable = False, autoincrement = True),
    Column('admin_user',String(20), nullable=False,index=True), ##管理员帐号
    Column('subtime', DateTime ,nullable=False,index = True),##操作时间
    Column('func_name',String(20), nullable=False,index=True), ##管理员帐号
    Column('status', Integer), 
    Column('content',BLOB), ##request.POST
    mysql_engine='InnoDB',
)




#------------------------------------------------------------------------------
#config历史记录
ConfigLog_table = Table('config_log', meta,
    Column('id', Integer, primary_key = True, nullable = False),	#log id
    Column('cname', String(32), index=True, nullable = False),
    Column('cval', BLOB), 
    Column('admin_user', String(32)),
    Column('sub_time', DateTime),
    mysql_engine='InnoDB',
)
#test_config历史记录
TestConfigLog_table = Table('test_config_log', meta,
    Column('id', Integer, primary_key = True, nullable = False),	#log id
    Column('cname', String(32), index=True, nullable = False),
    Column('cval', BLOB),
    Column('admin_user', String(32)),
    Column('sub_time', DateTime),
    mysql_engine='InnoDB',
)
#后台配置
configs_table = Table('configs', meta,
    Column('config_name', String(32), primary_key=True, nullable = False, autoincrement = False),
    Column('config_value', BLOB),
    Column('sub_time', DateTime),
    Column('admin_user', String(32)),
    mysql_engine='InnoDB',
)

#测试配置
test_configs_table = Table('test_configs', meta,
    Column('config_name', String(32), primary_key=True, nullable = False, autoincrement = False),
    Column('config_value', BLOB),
    Column('sub_time', DateTime),
    Column('admin_user', String(32)),
    mysql_engine='InnoDB',
)


Cache_table = Table('cache_table', meta,
    Column('cache_key', String(128), primary_key=True, nullable = False, autoincrement = False),
    Column('cache_val',BLOB), # need mediumblo:k
    Column('expire_time', DateTime ,nullable=False,index = True),
    mysql_engine='InnoDB',
)

Adclick_table = Table('adclick', meta,
    Column('imei', String(64), primary_key=True, nullable=False),
    Column('callback_url', String(1024)),
    Column('status', Integer, nullable=False),
    Column('is_ad', Integer),
    Column('pf', String(64)), 
    Column('uid', Integer),	#用户id
    Column('click_time', DateTime),
    Column('install_time', DateTime),
    mysql_engine='InnoDB',
)

#发奖记录
Rewards_table = Table('rewards', meta,
    Column('id', Integer, primary_key=True, nullable = False, autoincrement = True),
    Column('name', String(30)), 
    Column('reward_obj', String(30)),
    Column('obj_content', BLOB),
    Column('start_time', DateTime, index=True),
    Column('end_time', DateTime, index=True),
    Column('status', Integer, index=True),
    Column('admin_user', String(30)),
    Column('user_ip', String(20)),
    Column('add_time', DateTime),
    Column('update_time', DateTime),
    Column('rewards_dict', BLOB), 
    Column('pfs', BLOB), 
    Column('name_info', BLOB), 
    mysql_engine='InnoDB',
)


#兑奖码
RewardCode_table = Table('reward_code', meta,
    Column('code', String(32), primary_key=True, nullable = False),
    Column('rid', Integer, nullable = False), 
    Column('status', Integer, nullable = False), 
    Column('uid', Integer),	#用户id
    mysql_engine='InnoDB',
)


#跨服战
#此表结构在service.database_backup中有复制，如修改须同步
UserDuplicate_table = Table('user_duplicate', meta,
    Column('udid', String(32), primary_key = True, nullable = False),
    Column('uid', String(16), index = True, nullable = False),
    Column('pf', String(64)),
    Column('zone', String(8), index=True, nullable = False),
    Column('group_id', String(16), index = True, nullable = False),
    Column('uname', String(64), nullable = False),
    Column('head', String(64)),
    Column('level', Integer, index=True, nullable = False),
    Column('prestige', Integer, index=True, nullable = False),
    Column('join_time', DateTime, index=True),
    Column('open_date', Integer, index=True, nullable = False),
    Column('hero_data', BLOB), 
    Column('troop_data', BLOB), 
    Column('power', Integer, nullable = False),
    Column('pay_money', Integer, nullable = False), ## 截止报名时的充值总额
    Column('admin_pay', Integer, nullable = False), ## 截止报名时的虚拟充值总额
    Column('dnum', Integer, nullable = False), ## 跨服server序号
    mysql_engine='InnoDB',
)

DuplicateLog_table = Table('duplicate_log', meta,
    Column('dnum_date', String(32), primary_key = True, nullable = False),
    Column('group_id', String(16), index = True, nullable = False),
    Column('level', Integer, index=True, nullable = False),
    Column('open_date', Integer, index=True, nullable = False),
    Column('dnum', Integer, index=True, nullable = False),
    Column('world', BLOB), 
    Column('user', BLOB), 
    mysql_engine='InnoDB',
)

#后台用户充值记录
AdminPayRecords_table = Table('admin_pay_records', meta,
    Column('id', Integer, primary_key=True, nullable=False, autoincrement=True),
    Column('uid', String(32), index=True, nullable=False),
    Column('admin_user', String(32), index=True, nullable=False),
    Column('pay_money', Integer, nullable=False),
    Column('last_zone', String(32), nullable=False),
    Column('last_uname', String(32), nullable=False),
    Column('first_time', DateTime),
    Column('last_time', DateTime),
    mysql_engine='InnoDB',
)

#分区表
Zones_table = Table('zones', meta,
    Column('id', Integer, primary_key=True, nullable=False, autoincrement=True),
    Column('zone_id', String(64), index=True, unique=True, nullable=False),
    Column('zone_name', String(64), index=True, nullable=False),  #区名
    Column('host', String(64)),   #IP(域名)
    Column('port', Integer, index=True, nullable=False),  #端口
    Column('open_time', DateTime),  #开服时间
    Column('register_limit', Integer, nullable=False),  #注册限制，0超过N天不可注册1可注册
    Column('state', Integer, nullable=False),   #区服状态，0无1爆2新
    Column('recommend', Integer, nullable=False),   #0正常1强行推荐
    Column('merge_times', Integer, nullable=False),  #和服状态
    Column('active', Integer, index=True, nullable=False),  #激活状态 0未激活1激活
    Column('zone_group', String(64)),  #所属分组
    Column('op_merge_times', Integer, nullable=True, default=0),  #合服次数
    mysql_engine='InnoDB',
)

#合区记录
merge_record_table = Table('merge_records', meta,
    Column('id', Integer, primary_key=True, nullable=False, autoincrement=True),
    Column('admin_user', String(32), index=True, nullable=False),
    Column('merge_config', BLOB),               #合服配置
    Column('view_data', BLOB),                  #展示数据
    Column('finish_zones', BLOB),               #合服完成的原区ID
    Column('sub_time', DateTime, index=True),
    Column('start_time', DateTime),             #合服开始时间
    Column('end_time', DateTime),               #合服结束时间
    Column('progress', Integer, nullable=False),#合服进度
    Column('status', Integer, nullable=False),  #状态 0待提交，1已提交，2合服中，3合服完毕
    Column('server_host', BLOB),                #被合服区的host:port记录
    mysql_engine='InnoDB',
)


#代币发送记录
send_ucoin_log_table = Table('send_ucoin_log', meta,
    Column('id', Integer, primary_key=True, nullable=False, autoincrement=True),
    Column('uids', String(1024), nullable=False),
    Column('admin_user', String(32), index=True, nullable=False),
    Column('ucoin', Integer, nullable=False),
    Column('remark', String(256), nullable=False),
    Column('subtime', DateTime, nullable=False),
    Column('status', Integer, nullable=False),  #状态 0待发送，1已发送
    mysql_engine='InnoDB',
)

# JS接口测试记录
call_interface_records_table = Table('call_interface_records', meta,
    Column('id', Integer, primary_key=True, nullable=False, autoincrement=True),
    Column('op_user', String(32), index=True, nullable=False),
    Column('time', DateTime, nullable=False),
    Column('req_data', BLOB),                   #请求数据
    Column('rsp_data', BLOB),                   #测试结果
    mysql_engine='InnoDB',
)

gather_players_table = Table('gather_players', meta,
    Column('uid', String(32), primary_key=True, nullable=False),
    Column('zone', String(64), index=True, nullable=False),
    Column('status', Integer, index=True, nullable=False), 
    Column('days', Integer, nullable=False), 
    Column('score', Integer, nullable=False), 
    Column('times', Integer, nullable=False), 
    Column('add_time', DateTime),  #注册时间
    Column('login_time', DateTime),  #最后登录时间
    Column('last_time', DateTime),  #最后一次采集时间
    Column('discard_time', DateTime),  #废弃时间
    Column('last_did', Integer, nullable=True),  #最后一次采集模板数据ID
    Column('day_score', BLOB),
    mysql_engine='InnoDB',
)


gather_player_data_table = Table('gather_player_data', meta,
    Column('id', Integer, primary_key=True, nullable=False, autoincrement=True),
    Column('did', String(32), index=True, nullable=False),  #模板数据ID
    Column('uid', String(32), index=True, nullable=False),
    Column('time', DateTime, nullable=False),
    Column('data', BLOB), 
    mysql_engine='InnoDB',
)


#邀请记录
invitation_records_table = Table('invitation_records', meta,
    Column('uid', String(32), primary_key=True, nullable=False),
    Column('data', BLOB), 
    mysql_engine='InnoDB',
)

#IP地址账号登录记录
ipaddress_records_table = Table('ipaddress_records', meta,
    Column('ip', String(32), primary_key=True, nullable=False),
    Column('count', Integer, index=True, nullable=False, default=0), # 登录账号计数
    Column('data', BLOB), 
    mysql_engine='InnoDB',
)
