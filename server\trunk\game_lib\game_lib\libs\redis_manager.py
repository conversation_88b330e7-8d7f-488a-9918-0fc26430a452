# -*- coding: utf-8 -*-
"""
Redis连接管理器
统一管理Redis连接池，提供高性能缓存服务
"""

import redis
import zlib
import cPickle as pickle
import threading
import time
from django.conf import settings


class RedisManager(object):
    """Redis连接管理器 - 单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(RedisManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self.pools = {}
        self.clients = {}
        self._setup_connections()
    
    def _setup_connections(self):
        """设置Redis连接"""
        redis_config = getattr(settings, 'REDIS_CONFIG', {})
        
        # 主缓存连接池
        self.pools['main'] = redis.ConnectionPool(
            host=redis_config.get('host', '127.0.0.1'),
            port=redis_config.get('port', 6379),
            db=redis_config.get('db', 0),
            password=redis_config.get('password'),
            max_connections=redis_config.get('max_connections', 100),
            retry_on_timeout=redis_config.get('retry_on_timeout', True),
            socket_timeout=redis_config.get('socket_timeout', 5),
            socket_connect_timeout=redis_config.get('socket_connect_timeout', 5),
            socket_keepalive=redis_config.get('socket_keepalive', True),
            health_check_interval=redis_config.get('health_check_interval', 30)
        )
        
        # 会话缓存连接池 (DB 1)
        self.pools['session'] = redis.ConnectionPool(
            host=redis_config.get('host', '127.0.0.1'),
            port=redis_config.get('port', 6379),
            db=1,  # 使用DB 1存储会话数据
            password=redis_config.get('password'),
            max_connections=50,  # 会话连接数适当减少
            retry_on_timeout=True,
            socket_timeout=3,
            socket_connect_timeout=3,
            socket_keepalive=True,
            health_check_interval=30
        )
        
        # 统计数据缓存连接池 (DB 2)
        self.pools['analytics'] = redis.ConnectionPool(
            host=redis_config.get('host', '127.0.0.1'),
            port=redis_config.get('port', 6379),
            db=2,  # 使用DB 2存储统计数据
            password=redis_config.get('password'),
            max_connections=30,  # 统计连接数更少
            retry_on_timeout=True,
            socket_timeout=5,
            socket_connect_timeout=5,
            socket_keepalive=True,
            health_check_interval=60
        )
        
        # 创建Redis客户端
        for pool_name, pool in self.pools.items():
            self.clients[pool_name] = redis.Redis(
                connection_pool=pool,
                decode_responses=False,
                socket_read_size=65536
            )
    
    def get_client(self, pool_name='main'):
        """获取Redis客户端"""
        return self.clients.get(pool_name, self.clients['main'])
    
    def set_compressed(self, key, value, expiration=60*60*24*30, pool_name='main'):
        """设置压缩数据"""
        try:
            client = self.get_client(pool_name)
            compressed_value = zlib.compress(pickle.dumps(value))
            client.set(key, compressed_value)
            client.expire(key, expiration)
            return True
        except Exception as e:
            print(f"Redis set_compressed error: {e}")
            return False
    
    def get_compressed(self, key, default=None, pool_name='main'):
        """获取压缩数据"""
        try:
            client = self.get_client(pool_name)
            res = client.get(key)
            if res:
                return pickle.loads(zlib.decompress(res))
            return default
        except Exception as e:
            print(f"Redis get_compressed error: {e}")
            return default
    
    def set_json(self, key, value, expiration=60*60*24*30, pool_name='main'):
        """设置JSON数据"""
        try:
            import json
            client = self.get_client(pool_name)
            json_value = json.dumps(value, ensure_ascii=False)
            client.set(key, json_value)
            client.expire(key, expiration)
            return True
        except Exception as e:
            print(f"Redis set_json error: {e}")
            return False
    
    def get_json(self, key, default=None, pool_name='main'):
        """获取JSON数据"""
        try:
            import json
            client = self.get_client(pool_name)
            res = client.get(key)
            if res:
                return json.loads(res)
            return default
        except Exception as e:
            print(f"Redis get_json error: {e}")
            return default
    
    def delete(self, key, pool_name='main'):
        """删除键"""
        try:
            client = self.get_client(pool_name)
            return client.delete(key)
        except Exception as e:
            print(f"Redis delete error: {e}")
            return False
    
    def exists(self, key, pool_name='main'):
        """检查键是否存在"""
        try:
            client = self.get_client(pool_name)
            return client.exists(key)
        except Exception as e:
            print(f"Redis exists error: {e}")
            return False
    
    def incr(self, key, amount=1, pool_name='main'):
        """递增计数器"""
        try:
            client = self.get_client(pool_name)
            return client.incr(key, amount)
        except Exception as e:
            print(f"Redis incr error: {e}")
            return None
    
    def expire(self, key, time, pool_name='main'):
        """设置过期时间"""
        try:
            client = self.get_client(pool_name)
            return client.expire(key, time)
        except Exception as e:
            print(f"Redis expire error: {e}")
            return False
    
    def pipeline(self, pool_name='main'):
        """获取管道对象"""
        try:
            client = self.get_client(pool_name)
            return client.pipeline()
        except Exception as e:
            print(f"Redis pipeline error: {e}")
            return None
    
    def get_connection_info(self):
        """获取连接信息"""
        info = {}
        for pool_name, client in self.clients.items():
            try:
                pool = self.pools[pool_name]
                info[pool_name] = {
                    'created_connections': pool._created_connections,
                    'available_connections': len(pool._available_connections),
                    'in_use_connections': len(pool._in_use_connections),
                    'max_connections': pool.max_connections,
                }
            except Exception as e:
                info[pool_name] = {'error': str(e)}
        return info
    
    def health_check(self):
        """健康检查"""
        results = {}
        for pool_name, client in self.clients.items():
            try:
                start_time = time.time()
                client.ping()
                response_time = (time.time() - start_time) * 1000
                results[pool_name] = {
                    'status': 'healthy',
                    'response_time_ms': response_time
                }
            except Exception as e:
                results[pool_name] = {
                    'status': 'unhealthy',
                    'error': str(e)
                }
        return results


# 全局Redis管理器实例
redis_manager = RedisManager()


# 兼容性函数，保持原有接口
def get_cache(cache_config):
    """兼容原有的get_cache函数"""
    if isinstance(cache_config, (list, tuple)) and len(cache_config) >= 4:
        # 原有的(host, port, db, password)格式
        return RedisCache(cache_config[0], cache_config[1], cache_config[2], cache_config[3])
    else:
        # 返回默认的Redis客户端
        return redis_manager.get_client()


class RedisCache(object):
    """兼容原有RedisCache类"""
    
    def __init__(self, host, port, db, password):
        self.redis_manager = redis_manager
        # 根据db选择不同的连接池
        if db == 1:
            self.pool_name = 'session'
        elif db == 2:
            self.pool_name = 'analytics'
        else:
            self.pool_name = 'main'
    
    def set(self, key, value, expiration=60*60*24*30):
        return self.redis_manager.set_compressed(key, value, expiration, self.pool_name)
    
    def get(self, key, default=None):
        return self.redis_manager.get_compressed(key, default, self.pool_name)
    
    def delete(self, key):
        return self.redis_manager.delete(key, self.pool_name)
    
    def getset(self, key, value, expiration=2):
        try:
            client = self.redis_manager.get_client(self.pool_name)
            res = client.getset(key, value)
            client.expire(key, expiration)
            return res
        except Exception as e:
            print(f"Redis getset error: {e}")
            return None
    
    def flushdb(self):
        try:
            client = self.redis_manager.get_client(self.pool_name)
            return client.flushdb()
        except Exception as e:
            print(f"Redis flushdb error: {e}")
            return False
