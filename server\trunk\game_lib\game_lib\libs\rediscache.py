#-*- coding: utf-8 -*-
import redis
import zlib
import c<PERSON>ickle as pickle
from django.conf import settings


class RedisCache(object):
    '''
    cache
    '''
    def __init__(self, host, port, db, password):
        # 从settings获取Redis配置
        from django.conf import settings
        redis_config = getattr(settings, 'REDIS_CONFIG', {})

        # 优化Redis连接池配置
        self.cache_pool = redis.ConnectionPool(
            host=host or redis_config.get('host', '127.0.0.1'),
            port=port or redis_config.get('port', 6379),
            db=db or redis_config.get('db', 0),
            password=password or redis_config.get('password'),
            max_connections=redis_config.get('max_connections', 100),
            retry_on_timeout=redis_config.get('retry_on_timeout', True),
            socket_timeout=redis_config.get('socket_timeout', 5),
            socket_connect_timeout=redis_config.get('socket_connect_timeout', 5),
            socket_keepalive=redis_config.get('socket_keepalive', True),
            socket_keepalive_options={},
            health_check_interval=redis_config.get('health_check_interval', 30)
        )
        self.cache_redis = redis.Redis(
            connection_pool=self.cache_pool,
            decode_responses=False,  # 保持二进制数据
            socket_read_size=65536   # 增加读取缓冲区
        )


    def set(self, key, value, expiration=60*60*24*30):

        value = zlib.compress(pickle.dumps(value))
        self.cache_redis.set(key, value)
        self.cache_redis.expire(key, expiration)

    def get(self, key, default=None):
        res = self.cache_redis.get(key)
        if res:
            res = pickle.loads(zlib.decompress(res))
        elif res is None:
            res = default
        return res

    def delete(self, key):

        return self.cache_redis.delete(key)
    
    def getset(self, key, value, expiration=2):
        res = self.cache_redis.getset(key, value)
        self.cache_redis.expire(key, expiration)
        return res

    def flushdb(self):
        return self.cache_redis.flushdb()


def get_cache(s):

    host = s[0]
    port = s[1]
    db = s[2]
    password = s[3]

    return RedisCache(host,port,db,password)
