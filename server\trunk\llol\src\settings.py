#-*- coding: utf-8 -*-
import sys, os, os.path
import json

DEBUG = True
SUBTEST = False

TIME_ZONE = 'Asia/Shanghai'
WHERE = 'local'
USER_TABLE_NUM = 0

## 接口验证
API_AUTH_KEY = 'yWSExXmzgwCYNlUVRfIMTtoHpcPvkhBn'
## elasticsearch
ES_URL = 'http://**************:9201/%s' % WHERE
## 自动开服、合服进程端口号
SERVER_MANAGE_PORT = 3352

## 后台登录是否需要短信验证
LOGIN_CODE_AUTH = False

## UID长短分离， True: 长链接中用长uid，短连接中用短uid False: 以前的规则
UID_SEPARATE = False

## 是否启用测试配置
TEST_CONFIG_ENABLE = False

## CDN 配置上传目录
CONFIG_CDN_PATH = None

## 用户数据存储使用的序列化方式 json, repr
USE_SERIALIZE = 'json' 

if WHERE == 'local':
    USER_TABLE_NUM = 47
    CONFIG_CDN_PATH = '/data/server/trunk/llol/scripts/configs'
    import socket
    #socket.setdefaulttimeout(1)

    #import socket
    socket.setdefaulttimeout(5)
    SUBTEST = True

    USE_CACHE = True
    LOGIN_CODE_AUTH = False
    TEST_CONFIG_ENABLE = True

    # s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
    # s.connect(('*******', 80))
    # ip = s.getsockname()[0]
    BASEROOT = '/data/server/trunk/llol'
    BASE_URL = 'http://**************:8500'
    BASE2_URL = ['http://**************:8500/json/']
    FIGHT_LOG_URL = ['http://**************:8500/json/']
    BATTLE_URL = 'http://**************:3001'
    BATTLE_SERVER = ['http://**************:3001']
    MEDIA_URL = 'http://**************:8500/static'
    CACHE_BACKEND = 'memcached://127.0.0.1:11211'

    CACHE_PRE = 'sg_local_'
    ANA_URL = 'http://**************:7701'   #统计转发服务器
    ANA2_URL = ''  #统计
    UID_INCEPT = 10000000
    #ANA_SERVER = '*************'
    #ANA_PORT = 18255

    PWD = 'sdljflasjdfldasjflk'
    USE_SSL = False
    DB_NAME = 'analytics_sg'
    CMEM_MC_BACKENDS = []
    # 优化数据库连接配置
    DB_CONNECTION_CONFIG = {
        'pool_size': 20,              # 连接池大小
        'max_overflow': 30,           # 最大溢出连接
        'pool_recycle': 3600,         # 连接回收时间(秒)
        'pool_pre_ping': True,        # 连接前检查
        'pool_timeout': 30,           # 获取连接超时
        'connect_args': {
            'connect_timeout': 10,    # 连接超时
            'read_timeout': 30,       # 读取超时
            'write_timeout': 30,      # 写入超时
            'charset': 'utf8mb4',     # 字符集
            'autocommit': True,       # 自动提交
        }
    }

    DB_ACCOUNT_PASSWORD = {
        'shards': {
            '1': {
                # 'master': ('xiguyi_929288', 'hzg929288*', 'rm-bp1hbz75p8m521k1s.mysql.rds.aliyuncs.com', '3306', 'sglocal'),
                # 'slaver': ('xiguyi_929288', 'hzg929288*', 'rm-bp1hbz75p8m521k1s.mysql.rds.aliyuncs.com', '3306', 'sglocal'),
                'master': ('root', '?u[yBH7TmCYDW{a6', '127.0.0.1', '3306', 'SgLocal'),
                'slaver': ('root', '?u[yBH7TmCYDW{a6', '127.0.0.1', '3306', 'SgLocal'),
                'cache_backend': 'redis://127.0.0.1:6379/0',  # 改用Redis替代Memcached
            },
        },
    }

    # Redis连接配置
    REDIS_CONFIG = {
        'host': '127.0.0.1',
        'port': 6379,
        'db': 0,
        'password': None,
        'max_connections': 100,       # 最大连接数
        'retry_on_timeout': True,     # 超时重试
        'socket_timeout': 5,          # socket超时
        'socket_connect_timeout': 5,  # 连接超时
        'socket_keepalive': True,     # 保持连接
        'health_check_interval': 30,  # 健康检查间隔
    }

    MONGO_ACCOUNT = ('127.0.0.1', 27017)


## 加载后台文字、背景颜色配置
ADMIN_STYLE_CONFIG_DEFAULT = {
    "TITLE": "三本",
    "ICON": "sg.png",
    "APP_NAME": "local",
    "APP_NAME_CN": "●本地三国",
    "ADMIN_COLOR_MENU": "#335588",
    "ADMIN_COLOR_TITLE": "#0066FF",
    "ADMIN_COLOR_BG": "#b3b5b9"
}

if sys.platform == 'win32':
    ADMIN_STYLE_CONFIG_FILE = BASEROOT + r'\src\admin_style.json'
else:
    ADMIN_STYLE_CONFIG_FILE = BASEROOT + '/src/admin_style.json'
if os.path.exists(ADMIN_STYLE_CONFIG_FILE):
    ADMIN_STYLE_CONFIG = json.load(file(ADMIN_STYLE_CONFIG_FILE), encoding='utf-8').get(WHERE, ADMIN_STYLE_CONFIG_DEFAULT)
else:
    ADMIN_STYLE_CONFIG = ADMIN_STYLE_CONFIG_DEFAULT

TEMPLATE_DEBUG = DEBUG
    
from libs.zhifubao import ZhiFuBao
ZFB = {
    'partner': '****************',
    'key': '8a6azequdccenb836so82kgmv1xoqp70',

    'notify_url': '%s/zfb_notify/' % BASE_URL,
    'call_back_url': '%s/zfb_callback/' % BASE_URL,
    'api': ZhiFuBao,
}

UIDADD = 1000000000
DB_ECHO = False
if sys.platform == 'win32':
    QH_LOG_FILE = 'data\logs\syslog_360.log'
    ADQH_LOG_FILE = 'data\logs\syslog_ad_360.log'
    sys.path.insert(0, os.path.join(BASEROOT, '..\\', 'game_lib'))
    sys.path.insert(0, os.path.join(BASEROOT, '..\\', 'admin'))
else:
    QH_LOG_FILE = '/data/logs/syslog_360.log'
    ADQH_LOG_FILE = '/data/logs/syslog_ad_360.log'
    sys.path.insert(0, os.path.join(BASEROOT, '../', 'game_lib'))
    sys.path.insert(0, os.path.join(BASEROOT, '../', 'admin'))

USEING_SHARD_INDEX = '1'


LANGUAGE_CODE = 'zh-cn'

SITE_ID = 1

USE_I18N = False

MEDIA_ROOT = BASEROOT + "/static"

PFLIST= [
        ('local',u'local'),
        ('ios',u'Ios'),
        ('ios_djcy',u'Ios新'),
        ('test', u'官包'),
        ('test_dx', u'官包dx'),
        ('test_zh',u'卓杭'),
        ('hw',u'华为'),
        ('uc',u'Uc'),
        ('mi',u'小米'),
        ('juedi',u'绝地'),
        ('oppo','oppo'),
        ('vivo','vivo'),
        ('mi',u'小米'),
        ('yyb',u'应用宝'),
        ('test_taptap','taptap'),
        ]



SECRET_KEY = 'a&(b5!29e#2d0c$a813%be^ac&71a*^&76169e9a941'

TEMPLATE_LOADERS = (
    'django.template.loaders.filesystem.load_template_source',
    'django.template.loaders.app_directories.load_template_source',
)

#SESSION_ENGINE = "django.contrib.sessions.backends.cache"
SESSION_ENGINE = "game_lib.common.middlewares.session_cache"

MIDDLEWARE_CLASSES = (
    'django.middleware.common.CommonMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'game_lib.common.middlewares.simple.P3PMiddleware',
    'game_lib.common.middlewares.simple.AuthMiddleware',
)

TEMPLATE_CONTEXT_PROCESSORS = (
    'django.core.context_processors.request',
    'game_lib.common.context_processors.settings',
)

ROOT_URLCONF = 'urls'

TEMPLATE_DIRS = (
    BASEROOT + "/src/templates"
)

SESSION_EXPIRE_AT_BROWSER_CLOSE = True
SESSION_COOKIE_AGE = 60*60*24*10
PVP_CAMP_LIST_TIME = 60*60
SESSION_COOKIE_NAME = 'sessionid'

INSTALLED_APPS = (
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'admin',
)

if WHERE == 'local':
    INSTALLED_APPS = (
        'django.contrib.contenttypes',
        'django.contrib.sessions',
        'admin',
        #'concurrent_server'
    )

# 合服需要清理删除的数据
MERGE_DELETE_ATTR = {
    ##
    'user_del_key_list': [
        'credit_settle',
        'quota_gift',
        'year_kill_num',
        'year_kill_troop',
        'year_dead_num',
        'year_build',
        'banned_users',
        'is_notify',
        'mayor_get_time',
        'impeach_time',
        'xyz',
        'mining',

        'alien_reward',
        'pk_records',
        'climb_records',
        'hero_catch',
        'milepost_reward',
        'milepost_fight_reward',
        'estate',
        'visit',
        'credit_year',
        'credit',
        'year_credit',
        'credit_lv',
        'is_credit_lv_up',
        'credit_get_gifts',
        'credit_rool_gifts_num',
        'gtask',
        'ng_task',
        'ftask',
        'pk_npc',
        'city_build',
        'credit_dict',
        'counter_free_drop_days',
        'big_shot',
        'peach',
    ],
    'user_del_records_key_list': [
        'worship_time',
        'pay_ploy',
        'city_build_event',
        'sale_shop',
        'estate_6_hids',
        'happy_buy',
        'coin_consume',
        'limit_free',
        'dial',
        'treasure',
        'redbag_num',
        'pay_gtask_reward',
        'exchange_shop',
        'pay_choose',
        'rool_pay',
        'rool_pay_new_rewards',
        'auction',
        'equip_box',
        'pay_time',
        'food_king',
    ],
    'user_del_total_records_key_list': [
        'kill_num_merge',
        'die_num_merge',
        'build_count_merge',
        'climb_kill_wave',
    ]
}
