#!/bin/bash

# 优化的游戏服务器启动脚本
# 支持多线程、负载均衡和资源监控

# 配置参数
LOG_DIR="/data/server/logs"
PID_DIR="/data/server/pids"
MONITOR_DIR="/data/server/monitor"

# 创建必要目录
mkdir -p "$LOG_DIR" "$PID_DIR" "$MONITOR_DIR"

# 针对8核16G服务器的优化配置
# 主要使用多线程而不是多进程，避免数据同步问题
BACKEND_PROCESSES=1  # 后台服务单进程多线程
FIGHT_PROCESSES=2    # 战斗服务2个进程（无状态）
ZONE_PROCESSES=1     # 区服单进程多线程（共享游戏状态）

echo "配置进程数 - 后台服务: $BACKEND_PROCESSES, 战斗服务: $FIGHT_PROCESSES, 区服: $ZONE_PROCESSES"
echo "注意: 主要通过增加线程池大小来提升并发性能"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1${NC}" | tee -a "$LOG_DIR/startup.log"
}

log_warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARN: $1${NC}" | tee -a "$LOG_DIR/startup.log"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a "$LOG_DIR/startup.log"
}

# 检查进程是否运行
is_running() {
    local name="$1"
    local pid_file="$PID_DIR/${name}.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            return 0
        else
            rm -f "$pid_file"
            return 1
        fi
    fi
    return 1
}

# 启动服务函数
start_service() {
    local name="$1"
    local cmd="$2"
    local port="$3"
    local log_file="$LOG_DIR/${name}.log"
    local pid_file="$PID_DIR/${name}.pid"
    local monitor_file="$MONITOR_DIR/${name}.monitor"

    log_info "检查服务 [$name]"

    if is_running "$name"; then
        log_warn "$name 已在运行，跳过启动"
        return 0
    fi

    log_info "启动 $name (端口: $port)..."
    
    # 启动服务并记录PID
    nohup $cmd >> "$log_file" 2>&1 &
    local pid=$!
    echo $pid > "$pid_file"
    
    # 等待服务启动
    sleep 2
    
    if kill -0 "$pid" 2>/dev/null; then
        log_info "$name 启动成功！PID: $pid"

        # 创建监控文件
        cat > "$monitor_file" << EOF
{
    "name": "$name",
    "pid": $pid,
    "port": $port,
    "start_time": "$(date '+%Y-%m-%d %H:%M:%S')",
    "command": "$cmd",
    "log_file": "$log_file"
}
EOF
        return 0
    else
        log_error "$name 启动失败！查看日志: tail -10 $log_file"
        rm -f "$pid_file"
        return 1
    fi
}

# 停止服务函数
stop_service() {
    local name="$1"
    local pid_file="$PID_DIR/${name}.pid"
    local monitor_file="$MONITOR_DIR/${name}.monitor"

    log_info "停止服务 [$name]"

    # 先尝试从PID文件停止
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            log_info "发送TERM信号到进程 $pid"
            kill -TERM "$pid"

            # 等待进程优雅关闭
            local count=0
            while kill -0 "$pid" 2>/dev/null && [ $count -lt 15 ]; do
                sleep 1
                count=$((count + 1))
            done

            # 如果进程仍在运行，强制杀死
            if kill -0 "$pid" 2>/dev/null; then
                log_warn "进程 $pid 未响应TERM信号，发送KILL信号"
                kill -KILL "$pid"
                sleep 2

                # 再次确认进程已死
                if kill -0 "$pid" 2>/dev/null; then
                    log_error "无法杀死进程 $pid"
                fi
            fi

            log_info "$name 已停止"
        fi
        rm -f "$pid_file"
    else
        log_warn "未找到 $name 的PID文件，尝试按端口查找进程"

        # 根据服务名查找并杀死进程
        case "$name" in
            "Analytics_7701")
                local pids=$(lsof -t -i:7701 2>/dev/null)
                if [ -n "$pids" ]; then
                    log_info "找到占用7701端口的进程: $pids"
                    # 先创建PID文件，方便下次使用
                    echo "$pids" > "$pid_file"
                    kill -TERM $pids
                    sleep 2
                    kill -KILL $pids 2>/dev/null
                    log_info "$name 已停止"
                fi
                ;;
            "Backend_8500")
                local pids=$(lsof -t -i:8500 2>/dev/null)
                if [ -n "$pids" ]; then
                    log_info "找到占用8500端口的进程: $pids"
                    # 先创建PID文件，方便下次使用
                    echo "$pids" > "$pid_file"
                    kill -TERM $pids
                    sleep 2
                    kill -KILL $pids 2>/dev/null
                    log_info "$name 已停止"
                fi
                ;;
        esac
    fi

    rm -f "$monitor_file"
}

# 检查系统资源
check_system_resources() {
    log_info "检查系统资源..."
    
    # 检查内存
    local mem_total=$(free -m | awk 'NR==2{printf "%.0f", $2}')
    local mem_used=$(free -m | awk 'NR==2{printf "%.0f", $3}')
    local mem_free=$(free -m | awk 'NR==2{printf "%.0f", $4}')
    local mem_usage=$((mem_used * 100 / mem_total))
    
    log_info "内存使用情况: ${mem_used}MB/${mem_total}MB (${mem_usage}%)"
    
    if [ $mem_usage -gt 80 ]; then
        log_warn "内存使用率过高: ${mem_usage}%"
    fi
    
    # 检查磁盘空间
    local disk_usage=$(df -h /data | awk 'NR==2 {print $5}' | sed 's/%//')
    log_info "磁盘使用率: ${disk_usage}%"
    
    if [ $disk_usage -gt 80 ]; then
        log_warn "磁盘使用率过高: ${disk_usage}%"
    fi
    
    # 检查负载
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    log_info "系统负载: $load_avg"
}

# 显示服务状态
show_status() {
    log_info "服务状态概览:"
    echo "----------------------------------------"
    printf "%-20s %-10s %-8s %-10s\n" "服务名称" "状态" "PID" "端口"
    echo "----------------------------------------"
    
    # 检查已知服务的状态，自动创建缺失的PID文件
    declare -A services=(
        ["Analytics_7701"]="7701"
        ["Backend_8500"]="8500"
        ["Zone_1"]="5001"
        ["Zone_2"]="5002"
        ["Fight_3001"]="3001"
        ["Fight_3002"]="3002"
        ["Backup_SG3"]="0"
    )

    for service_name in "${!services[@]}"; do
        local port="${services[$service_name]}"
        local pid_file="$PID_DIR/${service_name}.pid"
        local monitor_file="$MONITOR_DIR/${service_name}.monitor"

        if [ -f "$pid_file" ]; then
            local pid=$(cat "$pid_file")
            # 验证PID文件中的进程是否真的是我们要的服务
            if kill -0 "$pid" 2>/dev/null; then
                # 进一步验证进程是否匹配端口（如果有端口的话）
                if [ "$port" != "0" ]; then
                    local port_pid=$(lsof -t -i:$port 2>/dev/null | head -1)
                    if [ "$port_pid" = "$pid" ]; then
                        printf "%-20s ${GREEN}%-10s${NC} %-8s %-10s\n" "$service_name" "运行中" "$pid" "$port"
                    else
                        # PID不匹配端口，删除错误的PID文件
                        rm -f "$pid_file" "$monitor_file"
                        if [ -n "$port_pid" ]; then
                            echo "$port_pid" > "$pid_file"
                            printf "%-20s ${GREEN}%-10s${NC} %-8s %-10s\n" "$service_name" "运行中" "$port_pid" "$port"
                        else
                            printf "%-20s ${RED}%-10s${NC} %-8s %-10s\n" "$service_name" "已停止" "-" "$port"
                        fi
                    fi
                else
                    printf "%-20s ${GREEN}%-10s${NC} %-8s %-10s\n" "$service_name" "运行中" "$pid" "$port"
                fi
            else
                # PID文件存在但进程不存在，删除PID文件
                rm -f "$pid_file" "$monitor_file"
                # 检查是否有其他进程占用端口
                if [ "$port" != "0" ]; then
                    local running_pid=$(lsof -t -i:$port 2>/dev/null | head -1)
                    if [ -n "$running_pid" ]; then
                        echo "$running_pid" > "$pid_file"
                        printf "%-20s ${GREEN}%-10s${NC} %-8s %-10s\n" "$service_name" "运行中" "$running_pid" "$port"
                    else
                        printf "%-20s ${RED}%-10s${NC} %-8s %-10s\n" "$service_name" "已停止" "-" "$port"
                    fi
                else
                    printf "%-20s ${RED}%-10s${NC} %-8s %-10s\n" "$service_name" "已停止" "-" "$port"
                fi
            fi
        else
            # PID文件不存在，检查端口是否被占用
            if [ "$port" != "0" ]; then
                local running_pid=$(lsof -t -i:$port 2>/dev/null | head -1)
                if [ -n "$running_pid" ]; then
                    # 发现服务在运行但没有PID文件，自动创建
                    echo "$running_pid" > "$pid_file"
                    printf "%-20s ${GREEN}%-10s${NC} %-8s %-10s\n" "$service_name" "运行中" "$running_pid" "$port"
                else
                    printf "%-20s ${RED}%-10s${NC} %-8s %-10s\n" "$service_name" "已停止" "-" "$port"
                fi
            else
                # 端口为0的服务（如备份服务），通过进程名查找
                local running_pid=$(pgrep -f "backup_start_sg3.py" 2>/dev/null)
                if [ -n "$running_pid" ]; then
                    echo "$running_pid" > "$pid_file"
                    printf "%-20s ${GREEN}%-10s${NC} %-8s %-10s\n" "$service_name" "运行中" "$running_pid" "$port"
                else
                    printf "%-20s ${RED}%-10s${NC} %-8s %-10s\n" "$service_name" "已停止" "-" "$port"
                fi
            fi
        fi
    done
    echo "----------------------------------------"
}

# 主程序
ACTION="$1"

case "$ACTION" in
    "start")
        log_info "========== 开始启动所有服务 =========="
        check_system_resources
        
        # 1. 启动统计服务
        start_service "Analytics_7701" "python /data/server/analytics_sg3/trunk/src/manage.py runserver 0.0.0.0:7701" "7701"
        
        # 2. 启动后台服务
        start_service "Backend_8500" "python /data/server/trunk/llol/src/manage.py runserver 0.0.0.0:8500" "8500"
        
        # 3. 启动战斗服务
        cd /data/server/service || { log_error "无法进入目录 /data/server/service"; exit 1; }
        start_service "Fight_3001" "node Laya.js 3001" "3001"
        start_service "Fight_3002" "node Laya.js 3002" "3002"
        
        # 4. 启动备份服务
        start_service "Backup_SG3" "python backup_start_sg3.py" "0"
        
        # 5. 启动区服（多线程优化）
        start_service "Zone_1" "python server.py --zone=1" "5001"
        start_service "Zone_2" "python server.py --zone=2" "5002"
        
        log_info "========== 所有服务启动完成 =========="
        show_status
        ;;
        
    "stop")
        log_info "========== 开始停止所有服务 =========="
        
        # 停止所有服务
        for pid_file in "$PID_DIR"/*.pid; do
            if [ -f "$pid_file" ]; then
                name=$(basename "$pid_file" .pid)
                stop_service "$name"
            fi
        done
        
        log_info "========== 所有服务已停止 =========="
        ;;
        
    "restart")
        log_info "========== 重启所有服务 =========="
        $0 stop
        sleep 5
        $0 start
        ;;
        
    "status")
        show_status
        check_system_resources
        ;;
        
    "monitor")
        # 实时监控模式
        log_info "进入实时监控模式 (按Ctrl+C退出)..."
        while true; do
            clear
            show_status
            check_system_resources
            sleep 5
        done
        ;;

    "restart-zone")
        ZONE_ID="$2"
        if [ -z "$ZONE_ID" ]; then
            echo "用法: $0 restart-zone <区服ID>"
            echo "例如: $0 restart-zone 1"
            exit 1
        fi

        log_info "========== 重启区服 $ZONE_ID =========="

        # 停止指定区服
        stop_service "Zone_$ZONE_ID"
        sleep 2

        # 启动指定区服
        cd /data/server/service || { log_error "无法进入目录 /data/server/service"; exit 1; }
        PORT=$((5000 + ZONE_ID))
        start_service "Zone_$ZONE_ID" "python server.py --zone=$ZONE_ID" "$PORT"

        log_info "========== 区服 $ZONE_ID 重启完成 =========="
        ;;

    "restart-fight")
        FIGHT_ID="$2"
        if [ -z "$FIGHT_ID" ]; then
            echo "用法: $0 restart-fight <战斗服ID>"
            echo "例如: $0 restart-fight 3001"
            exit 1
        fi

        log_info "========== 重启战斗服 $FIGHT_ID =========="

        # 停止指定战斗服
        stop_service "Fight_$FIGHT_ID"
        sleep 2

        # 启动指定战斗服
        cd /data/server/service || { log_error "无法进入目录 /data/server/service"; exit 1; }
        start_service "Fight_$FIGHT_ID" "node Laya.js $FIGHT_ID" "$FIGHT_ID"

        log_info "========== 战斗服 $FIGHT_ID 重启完成 =========="
        ;;

    "restart-backend")
        log_info "========== 重启后台服务 =========="

        # 停止后台服务
        stop_service "Backend_8500"
        sleep 2

        # 启动后台服务
        start_service "Backend_8500" "python /data/server/trunk/llol/src/manage.py runserver 0.0.0.0:8500" "8500"

        log_info "========== 后台服务重启完成 =========="
        ;;

    "restart-analytics")
        log_info "========== 重启统计服务 =========="

        # 停止统计服务
        stop_service "Analytics_7701"
        sleep 2

        # 启动统计服务
        start_service "Analytics_7701" "python /data/server/analytics_sg3/trunk/src/manage.py runserver 0.0.0.0:7701" "7701"

        log_info "========== 统计服务重启完成 =========="
        ;;

    *)
        echo "用法: $0 [命令] [参数]"
        echo ""
        echo "全局命令:"
        echo "  start           - 启动所有服务"
        echo "  stop            - 停止所有服务"
        echo "  restart         - 重启所有服务"
        echo "  status          - 显示服务状态"
        echo "  monitor         - 实时监控模式"
        echo ""
        echo "单服务命令:"
        echo "  restart-zone <ID>     - 重启指定区服 (例如: restart-zone 1)"
        echo "  restart-fight <PORT>  - 重启指定战斗服 (例如: restart-fight 3001)"
        echo "  restart-backend       - 重启后台服务"
        echo "  restart-analytics     - 重启统计服务"
        echo ""
        echo "示例:"
        echo "  $0 restart-zone 1     # 重启1区服务器"
        echo "  $0 restart-zone 2     # 重启2区服务器"
        echo "  $0 restart-fight 3001 # 重启3001端口战斗服"
        exit 1
        ;;
esac
