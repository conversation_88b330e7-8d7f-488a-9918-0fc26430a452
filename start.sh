#!/bin/bash

# 优化的游戏服务器启动脚本
# 支持多线程、负载均衡和资源监控

# 配置参数
LOG_DIR="/data/server/logs"
PID_DIR="/data/server/pids"
MONITOR_DIR="/data/server/monitor"

# 创建必要目录
mkdir -p "$LOG_DIR" "$PID_DIR" "$MONITOR_DIR"

# 正式服区服配置 - 在这里修改区服配置
declare -A ZONE_CONFIG
ZONE_CONFIG[1]="5001"    # 1区 - 端口5001
ZONE_CONFIG[4]="5004"    # 4区 - 端口5004
ZONE_CONFIG[5]="5005"    # 5区 - 端口5005
# ZONE_CONFIG[6]="5006"  # 6区 - 端口5006 (预留，需要时取消注释)

# 针对8核16G服务器的优化配置
# 主要使用多线程而不是多进程，避免数据同步问题
BACKEND_PROCESSES=1  # 后台服务单进程多线程
FIGHT_PROCESSES=1    # 战斗服务单进程（避免数据不一致风险）
ZONE_PROCESSES=1     # 区服单进程多线程（共享游戏状态）

# 战斗服务器配置说明：
# - 战斗服务器处理战斗计算，多进程可能导致：
#   1. 随机种子冲突
#   2. 战斗结果不一致
#   3. 负载分配不均
# - 建议使用单进程+高线程池，确保数据一致性

echo "配置进程数 - 后台服务: $BACKEND_PROCESSES, 战斗服务: $FIGHT_PROCESSES, 区服: $ZONE_PROCESSES"
echo "注意: 主要通过增加线程池大小来提升并发性能"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] INFO: $1${NC}" | tee -a "$LOG_DIR/startup.log"
}

log_warn() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARN: $1${NC}" | tee -a "$LOG_DIR/startup.log"
}

log_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}" | tee -a "$LOG_DIR/startup.log"
}

# 检查进程是否运行
is_running() {
    local name="$1"
    local pid_file="$PID_DIR/${name}.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            return 0
        else
            rm -f "$pid_file"
            return 1
        fi
    fi
    return 1
}

# 启动服务函数
start_service() {
    local name="$1"
    local cmd="$2"
    local port="$3"
    local log_file="$LOG_DIR/${name}.log"
    local pid_file="$PID_DIR/${name}.pid"
    local monitor_file="$MONITOR_DIR/${name}.monitor"

    log_info "检查服务 [$name]"

    if is_running "$name"; then
        log_warn "$name 已在运行，跳过启动"
        return 0
    fi

    log_info "启动 $name (端口: $port)..."
    
    # 启动服务并记录PID
    nohup $cmd >> "$log_file" 2>&1 &
    local pid=$!
    echo $pid > "$pid_file"
    
    # 等待服务启动（减少等待时间）
    sleep 1
    
    if kill -0 "$pid" 2>/dev/null; then
        log_info "$name 启动成功！PID: $pid"

        # 创建监控文件
        cat > "$monitor_file" << EOF
{
    "name": "$name",
    "pid": $pid,
    "port": $port,
    "start_time": "$(date '+%Y-%m-%d %H:%M:%S')",
    "command": "$cmd",
    "log_file": "$log_file"
}
EOF
        return 0
    else
        log_error "$name 启动失败！查看日志: tail -10 $log_file"
        rm -f "$pid_file"
        return 1
    fi
}

# 停止服务函数
stop_service() {
    local name="$1"
    local pid_file="$PID_DIR/${name}.pid"
    local monitor_file="$MONITOR_DIR/${name}.monitor"

    log_info "停止服务 [$name]"

    # 动态端口清理 - 根据服务名称自动识别端口
    local port=""
    case "$name" in
        "Analytics_7701")
            port="7701"
            ;;
        "Backend_8500")
            port="8500"
            ;;
        "Fight_3001")
            port="3001"
            ;;
        "Backup_SG3")
            # 备份服务特殊处理
            local pids=$(pgrep -f "backup_start_sg3.py" 2>/dev/null)
            if [ -n "$pids" ]; then
                log_info "强制停止备份进程: $pids"
                kill -KILL $pids 2>/dev/null
            fi
            ;;
        Zone_*)
            # 动态处理区服端口
            local zone_id="${name#Zone_}"
            if [[ -n "${ZONE_CONFIG[$zone_id]}" ]]; then
                port="${ZONE_CONFIG[$zone_id]}"
            fi
            ;;
    esac

    # 统一端口清理逻辑
    if [ -n "$port" ] && [ "$port" != "0" ]; then
        local pids=$(lsof -t -i:$port 2>/dev/null)
        if [ -n "$pids" ]; then
            log_info "强制停止${port}端口进程: $pids"
            kill -KILL $pids 2>/dev/null
        fi
    fi

    # 等待端口释放（减少等待时间）
    sleep 1

    # 清理文件
    rm -f "$pid_file" "$monitor_file"

    log_info "$name 已停止"
}

# 检查系统资源
check_system_resources() {
    log_info "检查系统资源..."
    
    # 检查内存
    local mem_total=$(free -m | awk 'NR==2{printf "%.0f", $2}')
    local mem_used=$(free -m | awk 'NR==2{printf "%.0f", $3}')
    local mem_free=$(free -m | awk 'NR==2{printf "%.0f", $4}')
    local mem_usage=$((mem_used * 100 / mem_total))
    
    log_info "内存使用情况: ${mem_used}MB/${mem_total}MB (${mem_usage}%)"
    
    if [ $mem_usage -gt 80 ]; then
        log_warn "内存使用率过高: ${mem_usage}%"
    fi
    
    # 检查磁盘空间
    local disk_usage=$(df -h /data | awk 'NR==2 {print $5}' | sed 's/%//')
    log_info "磁盘使用率: ${disk_usage}%"
    
    if [ $disk_usage -gt 80 ]; then
        log_warn "磁盘使用率过高: ${disk_usage}%"
    fi
    
    # 检查负载
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    log_info "系统负载: $load_avg"
}

# 显示服务状态
show_status() {
    log_info "服务状态概览:"
    echo "----------------------------------------"
    printf "%-20s %-10s %-8s %-10s\n" "服务名称" "状态" "PID" "端口"
    echo "----------------------------------------"
    
    # 检查已知服务的状态，自动创建缺失的PID文件
    # 动态构建服务列表，基于当前配置
    declare -A services=(
        ["Analytics_7701"]="7701"
        ["Backend_8500"]="8500"
        ["Fight_3001"]="3001"        # 单战斗服务器
        ["Backup_SG3"]="0"
    )

    # 动态添加配置的区服
    for zone_id in "${!ZONE_CONFIG[@]}"; do
        services["Zone_${zone_id}"]="${ZONE_CONFIG[$zone_id]}"
    done

    for service_name in "${!services[@]}"; do
        local port="${services[$service_name]}"

        if [ "$port" != "0" ]; then
            # 检查端口是否被占用，增加重试机制
            local pid=$(lsof -t -i:$port 2>/dev/null | head -1)
            if [ -z "$pid" ]; then
                # 第一次检查失败，等待1秒后重试
                sleep 1
                pid=$(lsof -t -i:$port 2>/dev/null | head -1)
            fi

            if [ -n "$pid" ]; then
                printf "%-20s ${GREEN}%-10s${NC} %-8s %-10s\n" "$service_name" "运行中" "$pid" "$port"
            else
                printf "%-20s ${RED}%-10s${NC} %-8s %-10s\n" "$service_name" "已停止" "-" "$port"
            fi
        else
            # 备份服务按进程名查找
            local pid=$(pgrep -f "backup_start_sg3.py" 2>/dev/null | head -1)
            if [ -n "$pid" ]; then
                printf "%-20s ${GREEN}%-10s${NC} %-8s %-10s\n" "$service_name" "运行中" "$pid" "$port"
            else
                printf "%-20s ${RED}%-10s${NC} %-8s %-10s\n" "$service_name" "已停止" "-" "$port"
            fi
        fi
    done
    echo "----------------------------------------"
}

# 主程序
ACTION="$1"

case "$ACTION" in
    "start")
        log_info "========== 开始启动所有服务 =========="
        check_system_resources
        
        # 1. 启动统计服务
        start_service "Analytics_7701" "python /data/server/analytics_sg3/trunk/src/manage.py runserver 0.0.0.0:7701" "7701"
        
        # 2. 启动后台服务
        start_service "Backend_8500" "python /data/server/trunk/llol/src/manage.py runserver 0.0.0.0:8500" "8500"
        
        # 3. 启动战斗服务（单进程，避免数据不一致）
        cd /data/server/service || { log_error "无法进入目录 /data/server/service"; exit 1; }
        start_service "Fight_3001" "node Laya.js 3001" "3001"
        log_info "战斗服务使用单进程配置，确保战斗结果一致性"
        
        # 4. 启动备份服务
        start_service "Backup_SG3" "python backup_start_sg3.py" "0"
        
        # 5. 启动区服（正式服配置：1区、4区、5区）
        cd /data/server/service || { log_error "无法进入目录 /data/server/service"; exit 1; }

        log_info "开始启动区服..."
        log_info "正式服区服配置："
        for zone_id in "${!ZONE_CONFIG[@]}"; do
            log_info "  ${zone_id}区 - 端口${ZONE_CONFIG[$zone_id]}"
        done

        # 按顺序启动区服（1区、4区、5区）
        for zone_id in 1 4 5; do
            if [[ -n "${ZONE_CONFIG[$zone_id]}" ]]; then
                port="${ZONE_CONFIG[$zone_id]}"
                log_info "启动${zone_id}区服务器 (端口${port})..."
                start_service "Zone_${zone_id}" "python server.py --zone=${zone_id}" "$port"

                # 避免数据库连接竞争，每启动一个区服等待3秒
                log_info "等待${zone_id}区启动完成..."
                sleep 3
            fi
        done
        
        log_info "========== 所有服务启动完成 =========="

        # 等待所有服务完全启动并绑定端口（减少等待时间）
        log_info "等待服务完全启动..."
        sleep 2

        show_status
        ;;
        
    "stop")
        log_info "========== 开始停止所有服务 =========="
        
        # 停止所有服务
        for pid_file in "$PID_DIR"/*.pid; do
            if [ -f "$pid_file" ]; then
                name=$(basename "$pid_file" .pid)
                stop_service "$name"
            fi
        done
        
        log_info "========== 所有服务已停止 =========="
        ;;

    "restart")
        log_info "========== 重启所有服务 =========="
        $0 stop
        sleep 2
        $0 start
        ;;
        
    "status")
        show_status
        check_system_resources
        ;;
        
    "monitor")
        # 实时监控模式
        log_info "进入实时监控模式 (按Ctrl+C退出)..."
        while true; do
            clear
            show_status
            check_system_resources
            sleep 5
        done
        ;;

    "restart-zone")
        ZONE_ID="$2"
        if [ -z "$ZONE_ID" ]; then
            echo "用法: $0 restart-zone <区服ID>"
            echo "例如: $0 restart-zone 1"
            exit 1
        fi

        log_info "========== 重启区服 $ZONE_ID =========="

        # 停止指定区服
        stop_service "Zone_$ZONE_ID"
        sleep 2

        # 启动指定区服
        cd /data/server/service || { log_error "无法进入目录 /data/server/service"; exit 1; }

        # 检查区服ID是否有效
        if [[ -z "${ZONE_CONFIG[$ZONE_ID]}" ]]; then
            log_error "不支持的区服ID: $ZONE_ID"
            log_error "支持的区服ID: ${!ZONE_CONFIG[@]}"
            log_error "正式服区服配置："
            for zone_id in "${!ZONE_CONFIG[@]}"; do
                log_error "  ${zone_id}区 - 端口${ZONE_CONFIG[$zone_id]}"
            done
            exit 1
        fi

        # 获取区服端口号
        PORT="${ZONE_CONFIG[$ZONE_ID]}"
        log_info "重启${ZONE_ID}区服务器 (端口${PORT})..."
        start_service "Zone_$ZONE_ID" "python server.py --zone=$ZONE_ID" "$PORT"

        log_info "========== 区服 $ZONE_ID 重启完成 =========="
        ;;

    "restart-fight")
        FIGHT_ID="$2"
        if [ -z "$FIGHT_ID" ]; then
            echo "用法: $0 restart-fight <战斗服ID>"
            echo "例如: $0 restart-fight 3001"
            exit 1
        fi

        log_info "========== 重启战斗服 $FIGHT_ID =========="

        # 停止指定战斗服
        stop_service "Fight_$FIGHT_ID"
        sleep 2

        # 启动指定战斗服
        cd /data/server/service || { log_error "无法进入目录 /data/server/service"; exit 1; }
        start_service "Fight_$FIGHT_ID" "node Laya.js $FIGHT_ID" "$FIGHT_ID"

        log_info "========== 战斗服 $FIGHT_ID 重启完成 =========="
        ;;

    "restart-backend")
        log_info "========== 重启后台服务 =========="

        # 停止后台服务
        stop_service "Backend_8500"
        sleep 2

        # 启动后台服务
        start_service "Backend_8500" "python /data/server/trunk/llol/src/manage.py runserver 0.0.0.0:8500" "8500"

        log_info "========== 后台服务重启完成 =========="
        ;;

    "restart-analytics")
        log_info "========== 重启统计服务 =========="

        # 停止统计服务
        stop_service "Analytics_7701"
        sleep 2

        # 启动统计服务
        start_service "Analytics_7701" "python /data/server/analytics_sg3/trunk/src/manage.py runserver 0.0.0.0:7701" "7701"

        log_info "========== 统计服务重启完成 =========="
        ;;

    *)
        echo "用法: $0 [命令] [参数]"
        echo ""
        echo "全局命令:"
        echo "  start           - 启动所有服务"
        echo "  stop            - 停止所有服务"
        echo "  restart         - 重启所有服务"
        echo "  status          - 显示服务状态"
        echo "  monitor         - 实时监控模式"
        echo ""
        echo "单服务命令:"
        echo "  restart-zone <ID>     - 重启指定区服 (支持区服: ${!ZONE_CONFIG[@]})"
        echo "  restart-fight <PORT>  - 重启指定战斗服 (例如: restart-fight 3001)"
        echo "  restart-backend       - 重启后台服务"
        echo "  restart-analytics     - 重启统计服务"
        echo ""
        echo "正式服区服配置:"
        for zone_id in "${!ZONE_CONFIG[@]}"; do
            echo "  ${zone_id}区 - 端口${ZONE_CONFIG[$zone_id]}"
        done
        echo ""
        echo "示例:"
        for zone_id in "${!ZONE_CONFIG[@]}"; do
            echo "  $0 restart-zone $zone_id     # 重启${zone_id}区服务器 (端口${ZONE_CONFIG[$zone_id]})"
        done
        echo "  $0 restart-fight 3001 # 重启3001端口战斗服"
        exit 1
        ;;
esac
