#!/bin/bash

# 正式服区服配置文件
# 用于管理区服ID和端口号的映射关系

# 区服配置数组 - 格式: "区服ID:端口号"
ZONE_CONFIGS=(
    "1:5001"    # 1区 - 端口5001
    "4:5004"    # 4区 - 端口5004  
    "5:5005"    # 5区 - 端口5005
    "6:5006"    # 6区 - 端口5006 (预留)
)

# 获取区服端口号
get_zone_port() {
    local zone_id="$1"
    
    for config in "${ZONE_CONFIGS[@]}"; do
        local id="${config%:*}"
        local port="${config#*:}"
        
        if [ "$id" = "$zone_id" ]; then
            echo "$port"
            return 0
        fi
    done
    
    # 如果没找到配置，返回错误
    return 1
}

# 获取所有区服ID列表
get_all_zone_ids() {
    for config in "${ZONE_CONFIGS[@]}"; do
        local id="${config%:*}"
        echo -n "$id "
    done
    echo
}

# 检查区服ID是否有效
is_valid_zone_id() {
    local zone_id="$1"
    local valid_ids=$(get_all_zone_ids)
    
    for id in $valid_ids; do
        if [ "$id" = "$zone_id" ]; then
            return 0
        fi
    done
    
    return 1
}

# 显示区服配置信息
show_zone_config() {
    echo "========== 正式服区服配置 =========="
    for config in "${ZONE_CONFIGS[@]}"; do
        local id="${config%:*}"
        local port="${config#*:}"
        echo "  ${id}区 - 端口${port}"
    done
    echo "=================================="
}

# 添加新区服配置
add_zone_config() {
    local zone_id="$1"
    local port="$2"
    
    if [ -z "$zone_id" ] || [ -z "$port" ]; then
        echo "错误: 请提供区服ID和端口号"
        echo "用法: add_zone_config <区服ID> <端口号>"
        return 1
    fi
    
    # 检查区服ID是否已存在
    if is_valid_zone_id "$zone_id"; then
        echo "错误: 区服ID $zone_id 已存在"
        return 1
    fi
    
    # 检查端口是否已被使用
    for config in "${ZONE_CONFIGS[@]}"; do
        local existing_port="${config#*:}"
        if [ "$existing_port" = "$port" ]; then
            echo "错误: 端口 $port 已被使用"
            return 1
        fi
    done
    
    echo "添加新区服配置: ${zone_id}区 - 端口${port}"
    echo "请手动编辑 zone_config.sh 文件，在 ZONE_CONFIGS 数组中添加:"
    echo "    \"${zone_id}:${port}\"    # ${zone_id}区 - 端口${port}"
}

# 如果直接运行此脚本，显示配置信息
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    case "${1:-show}" in
        "show")
            show_zone_config
            ;;
        "add")
            add_zone_config "$2" "$3"
            ;;
        "list")
            echo "支持的区服ID: $(get_all_zone_ids)"
            ;;
        *)
            echo "用法: $0 [show|add|list]"
            echo ""
            echo "命令说明:"
            echo "  show        - 显示当前区服配置"
            echo "  add <ID> <PORT> - 添加新区服配置"
            echo "  list        - 列出所有区服ID"
            ;;
    esac
fi
